#!/usr/bin/env python3
"""
Paper-inspired Minecraft Server
基于Paper服务器的架构理念，使用Python实现的高性能Minecraft服务器
"""

import asyncio
import struct
import json
import logging
import uuid
import time
import zlib
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConnectionState(Enum):
    """连接状态枚举"""
    HANDSHAKING = 0
    STATUS = 1
    LOGIN = 2
    PLAY = 3

@dataclass
class ServerConfig:
    """服务器配置"""
    host: str = '0.0.0.0'
    port: int = 25565
    max_players: int = 20
    motd: str = 'Paper-inspired Python Minecraft Server'
    view_distance: int = 10
    simulation_distance: int = 10
    enable_compression: bool = False
    compression_threshold: int = 256

class PacketBuffer:
    """数据包缓冲区 - 参考Paper的实现"""
    
    def __init__(self, data: bytes = b''):
        self.data = bytearray(data)
        self.pos = 0
    
    def write_varint(self, value: int):
        """写入VarInt"""
        while True:
            byte = value & 0x7F
            value >>= 7
            if value != 0:
                byte |= 0x80
            self.data.append(byte)
            if value == 0:
                break
    
    def read_varint(self) -> int:
        """读取VarInt"""
        value = 0
        position = 0
        
        while True:
            if self.pos >= len(self.data):
                raise ValueError("Unexpected end of buffer")
            
            byte = self.data[self.pos]
            self.pos += 1
            
            value |= (byte & 0x7F) << position
            
            if (byte & 0x80) == 0:
                break
                
            position += 7
            if position >= 32:
                raise ValueError("VarInt is too big")
                
        return value
    
    def write_string(self, text: str):
        """写入字符串"""
        text_bytes = text.encode('utf-8')
        self.write_varint(len(text_bytes))
        self.data.extend(text_bytes)
    
    def read_string(self) -> str:
        """读取字符串"""
        length = self.read_varint()
        if self.pos + length > len(self.data):
            raise ValueError("String length exceeds buffer")

        text = self.data[self.pos:self.pos + length].decode('utf-8')
        self.pos += length
        return text

    def read_bool(self) -> bool:
        """读取布尔值"""
        if self.pos >= len(self.data):
            raise ValueError("Unexpected end of buffer")
        value = self.data[self.pos] != 0
        self.pos += 1
        return value

    def read_byte(self) -> int:
        """读取字节"""
        if self.pos >= len(self.data):
            raise ValueError("Unexpected end of buffer")
        value = self.data[self.pos]
        self.pos += 1
        return value

    def read_short(self) -> int:
        """读取短整型"""
        if self.pos + 2 > len(self.data):
            raise ValueError("Unexpected end of buffer")
        value = struct.unpack('>h', self.data[self.pos:self.pos + 2])[0]
        self.pos += 2
        return value

    def read_int(self) -> int:
        """读取整型"""
        if self.pos + 4 > len(self.data):
            raise ValueError("Unexpected end of buffer")
        value = struct.unpack('>i', self.data[self.pos:self.pos + 4])[0]
        self.pos += 4
        return value

    def read_long(self) -> int:
        """读取长整型"""
        if self.pos + 8 > len(self.data):
            raise ValueError("Unexpected end of buffer")
        value = struct.unpack('>q', self.data[self.pos:self.pos + 8])[0]
        self.pos += 8
        return value

    def read_float(self) -> float:
        """读取浮点数"""
        if self.pos + 4 > len(self.data):
            raise ValueError("Unexpected end of buffer")
        value = struct.unpack('>f', self.data[self.pos:self.pos + 4])[0]
        self.pos += 4
        return value

    def read_double(self) -> float:
        """读取双精度浮点数"""
        if self.pos + 8 > len(self.data):
            raise ValueError("Unexpected end of buffer")
        value = struct.unpack('>d', self.data[self.pos:self.pos + 8])[0]
        self.pos += 8
        return value
    
    def write_bool(self, value: bool):
        """写入布尔值"""
        self.data.append(1 if value else 0)
    
    def write_byte(self, value: int):
        """写入字节"""
        self.data.append(value & 0xFF)
    
    def write_short(self, value: int):
        """写入短整型"""
        self.data.extend(struct.pack('>h', value))
    
    def write_int(self, value: int):
        """写入整型"""
        self.data.extend(struct.pack('>i', value))
    
    def write_long(self, value: int):
        """写入长整型"""
        self.data.extend(struct.pack('>q', value))
    
    def write_float(self, value: float):
        """写入浮点数"""
        self.data.extend(struct.pack('>f', value))
    
    def write_double(self, value: float):
        """写入双精度浮点数"""
        self.data.extend(struct.pack('>d', value))
    
    def write_uuid(self, uuid_str: str):
        """写入UUID"""
        # 简化UUID处理
        self.data.extend(b'\x00' * 16)
    
    def write_position(self, x: int, y: int, z: int):
        """写入位置"""
        # 确保坐标在有效范围内
        x = x & 0x3FFFFFF
        y = y & 0xFFF
        z = z & 0x3FFFFFF
        
        value = (x << 38) | (y << 26) | z
        self.write_long(value)
    
    def write_nbt_compound(self, data: Dict[str, Any] = None):
        """写入NBT复合标签"""
        if data is None:
            # 空NBT - 只写TAG_End
            self.data.append(0x00)
        else:
            # 简化的NBT实现
            self.data.append(0x0A)  # TAG_Compound
            self.write_string("")   # 空名称
            self.data.append(0x00)  # TAG_End
    
    def get_bytes(self) -> bytes:
        """获取字节数据"""
        return bytes(self.data)

class Packet:
    """数据包基类"""
    
    def __init__(self, packet_id: int):
        self.packet_id = packet_id
        self.buffer = PacketBuffer()
        self.buffer.write_varint(packet_id)
    
    def get_data(self) -> bytes:
        """获取完整的数据包数据"""
        packet_data = self.buffer.get_bytes()
        
        # 创建带长度前缀的完整数据包
        length_buffer = PacketBuffer()
        length_buffer.write_varint(len(packet_data))
        
        return length_buffer.get_bytes() + packet_data

class StatusResponsePacket(Packet):
    """状态响应包"""
    
    def __init__(self, status_json: str):
        super().__init__(0x00)
        self.buffer.write_string(status_json)

class PongPacket(Packet):
    """Pong响应包"""
    
    def __init__(self, payload: int):
        super().__init__(0x01)
        self.buffer.write_long(payload)

class LoginSuccessPacket(Packet):
    """登录成功包"""
    
    def __init__(self, uuid_str: str, username: str):
        super().__init__(0x02)
        self.buffer.write_uuid(uuid_str)
        self.buffer.write_string(username)

class JoinGamePacket(Packet):
    """加入游戏包 - 参考Paper的实现"""
    
    def __init__(self, entity_id: int, config: ServerConfig):
        super().__init__(0x28)  # 1.20.1的Join Game包ID
        
        # 实体ID
        self.buffer.write_int(entity_id)
        
        # 硬核模式
        self.buffer.write_bool(False)
        
        # 游戏模式
        self.buffer.write_varint(1)  # 创造模式
        
        # 之前的游戏模式
        self.buffer.write_varint(255)  # -1 (无)
        
        # 世界名称列表
        self.buffer.write_varint(1)
        self.buffer.write_string("minecraft:overworld")
        
        # 维度编解码器 (使用空NBT避免复杂性)
        self.buffer.write_nbt_compound()

        # 维度类型 (使用空NBT)
        self.buffer.write_nbt_compound()
        
        # 世界名称
        self.buffer.write_string("minecraft:overworld")
        
        # 种子哈希
        self.buffer.write_long(0)
        
        # 最大玩家数
        self.buffer.write_varint(config.max_players)
        
        # 视距
        self.buffer.write_varint(config.view_distance)
        
        # 模拟距离
        self.buffer.write_varint(config.simulation_distance)
        
        # 减少调试信息
        self.buffer.write_bool(False)
        
        # 启用重生屏幕
        self.buffer.write_bool(True)
        
        # 调试模式
        self.buffer.write_bool(False)
        
        # 平坦世界
        self.buffer.write_bool(True)
        
        # 死亡位置 (可选)
        self.buffer.write_bool(False)

class PlayerPositionAndLookPacket(Packet):
    """玩家位置和视角包"""
    
    def __init__(self, x: float, y: float, z: float, yaw: float, pitch: float, teleport_id: int = 0):
        super().__init__(0x3C)  # 1.20.1的包ID
        
        self.buffer.write_double(x)
        self.buffer.write_double(y)
        self.buffer.write_double(z)
        self.buffer.write_float(yaw)
        self.buffer.write_float(pitch)
        self.buffer.write_byte(0)  # 标志
        self.buffer.write_varint(teleport_id)

class PlayerAbilitiesPacket(Packet):
    """玩家能力包"""
    
    def __init__(self, creative_mode: bool = True):
        super().__init__(0x34)  # 1.20.1的包ID
        
        flags = 0
        if creative_mode:
            flags |= 0x01  # 无敌
            flags |= 0x02  # 飞行
            flags |= 0x04  # 允许飞行
            flags |= 0x08  # 创造模式
        
        self.buffer.write_byte(flags)
        self.buffer.write_float(0.05)  # 飞行速度
        self.buffer.write_float(0.1)   # 行走速度

class SpawnPositionPacket(Packet):
    """出生点包"""
    
    def __init__(self, x: int, y: int, z: int):
        super().__init__(0x50)  # 1.20.1的包ID
        
        self.buffer.write_position(x, y, z)
        self.buffer.write_float(0.0)  # 角度

class KeepAlivePacket(Packet):
    """Keep Alive包"""
    
    def __init__(self, keep_alive_id: int):
        super().__init__(0x24)  # 1.20.1的包ID
        
        self.buffer.write_long(keep_alive_id)

class Player:
    """玩家类 - 参考Paper的Player实现"""
    
    def __init__(self, username: str, uuid_str: str, writer: asyncio.StreamWriter):
        self.username = username
        self.uuid = uuid_str
        self.writer = writer
        self.entity_id = id(self) & 0x7FFFFFFF  # 简单的实体ID生成
        
        # 位置和旋转
        self.x = 0.5
        self.y = 100.0  # 在空中开始
        self.z = 0.5
        self.yaw = 0.0
        self.pitch = 0.0
        
        # 状态
        self.health = 20.0
        self.food = 20
        self.gamemode = 1  # 创造模式
        self.on_ground = False
        
        # 连接状态
        self.last_keep_alive = time.time()
        self.keep_alive_id = 0
    
    async def send_packet(self, packet: Packet):
        """发送数据包给玩家"""
        try:
            data = packet.get_data()
            self.writer.write(data)
            await self.writer.drain()
        except Exception as e:
            logger.debug(f"Failed to send packet to {self.username}: {e}")
    
    def is_connected(self) -> bool:
        """检查玩家是否仍然连接"""
        return not self.writer.is_closing()

class PaperInspiredServer:
    """Paper风格的Minecraft服务器"""
    
    def __init__(self, config: ServerConfig):
        self.config = config
        self.players: Dict[str, Player] = {}  # UUID -> Player
        self.running = False
        
    async def start(self):
        """启动服务器"""
        logger.info(f"Starting Paper-inspired Minecraft Server on {self.config.host}:{self.config.port}")
        logger.info(f"MOTD: {self.config.motd}")
        logger.info(f"Max players: {self.config.max_players}")
        
        # 启动Keep Alive任务
        asyncio.create_task(self._keep_alive_loop())
        
        # 启动服务器
        server = await asyncio.start_server(
            self._handle_client,
            self.config.host,
            self.config.port
        )
        
        self.running = True
        
        async with server:
            await server.serve_forever()
    
    async def stop(self):
        """停止服务器"""
        logger.info("Stopping server...")
        self.running = False
        
        # 断开所有玩家
        for player in list(self.players.values()):
            try:
                player.writer.close()
                await player.writer.wait_closed()
            except:
                pass
        
        self.players.clear()
    
    async def _handle_client(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter):
        """处理客户端连接"""
        addr = writer.get_extra_info('peername')
        logger.info(f"New client connection: {addr}")
        
        try:
            state = ConnectionState.HANDSHAKING
            player = None
            
            while True:
                # 读取数据包
                packet_data = await self._read_packet(reader)
                if packet_data is None:
                    break
                
                # 处理数据包
                if state == ConnectionState.HANDSHAKING:
                    state = await self._handle_handshaking(packet_data, writer)
                elif state == ConnectionState.STATUS:
                    await self._handle_status(packet_data, writer)
                elif state == ConnectionState.LOGIN:
                    result = await self._handle_login(packet_data, writer)
                    if isinstance(result, tuple):
                        state, player = result
                elif state == ConnectionState.PLAY:
                    if player:
                        await self._handle_play(packet_data, player)
                    else:
                        break
                        
        except Exception as e:
            logger.error(f"Error handling client {addr}: {e}")
        finally:
            if player and player.uuid in self.players:
                logger.info(f"Player {player.username} disconnected")
                del self.players[player.uuid]
            
            try:
                writer.close()
                await writer.wait_closed()
            except:
                pass
    
    async def _read_packet(self, reader: asyncio.StreamReader) -> Optional[bytes]:
        """读取数据包"""
        try:
            # 读取长度
            length = await self._read_varint(reader)
            if length is None or length <= 0:
                return None
            
            # 读取数据
            data = await reader.read(length)
            if len(data) != length:
                return None
            
            return data
        except:
            return None
    
    async def _read_varint(self, reader: asyncio.StreamReader) -> Optional[int]:
        """读取VarInt"""
        value = 0
        position = 0
        
        for _ in range(5):  # VarInt最多5字节
            byte_data = await reader.read(1)
            if not byte_data:
                return None
            
            byte = byte_data[0]
            value |= (byte & 0x7F) << position
            
            if (byte & 0x80) == 0:
                return value
            
            position += 7
        
        return None

    async def _handle_handshaking(self, packet_data: bytes, writer: asyncio.StreamWriter) -> ConnectionState:
        """处理握手状态"""
        buffer = PacketBuffer(packet_data)
        packet_id = buffer.read_varint()

        if packet_id == 0x00:  # 握手包
            protocol_version = buffer.read_varint()
            server_address = buffer.read_string()
            server_port = buffer.read_short()
            next_state = buffer.read_varint()

            logger.info(f"Handshake: protocol={protocol_version}, address={server_address}, port={server_port}, next_state={next_state}")

            if next_state == 1:
                return ConnectionState.STATUS
            elif next_state == 2:
                return ConnectionState.LOGIN

        return ConnectionState.HANDSHAKING

    async def _handle_status(self, packet_data: bytes, writer: asyncio.StreamWriter):
        """处理状态查询"""
        buffer = PacketBuffer(packet_data)
        packet_id = buffer.read_varint()

        if packet_id == 0x00:  # 状态请求
            status = {
                "version": {
                    "name": "1.20.1",
                    "protocol": 763
                },
                "players": {
                    "max": self.config.max_players,
                    "online": len(self.players),
                    "sample": []
                },
                "description": {
                    "text": self.config.motd
                },
                "favicon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            }

            packet = StatusResponsePacket(json.dumps(status))
            await self._send_packet(writer, packet)

        elif packet_id == 0x01:  # Ping请求
            payload = struct.unpack('>q', packet_data[1:9])[0]
            packet = PongPacket(payload)
            await self._send_packet(writer, packet)

    async def _handle_login(self, packet_data: bytes, writer: asyncio.StreamWriter) -> tuple:
        """处理登录状态"""
        buffer = PacketBuffer(packet_data)
        packet_id = buffer.read_varint()

        if packet_id == 0x00:  # 登录开始
            username = buffer.read_string()

            logger.info(f"Player {username} attempting to login")

            # 检查服务器是否已满
            if len(self.players) >= self.config.max_players:
                # 发送断开连接包
                disconnect_packet = Packet(0x00)
                disconnect_packet.buffer.write_string('{"text":"Server is full!"}')
                await self._send_packet(writer, disconnect_packet)
                return ConnectionState.LOGIN, None

            # 生成UUID
            player_uuid = str(uuid.uuid4())

            # 发送登录成功
            packet = LoginSuccessPacket(player_uuid, username)
            await self._send_packet(writer, packet)

            # 创建玩家对象
            player = Player(username, player_uuid, writer)
            self.players[player_uuid] = player

            # 发送游戏初始化序列
            await self._send_join_game_sequence(player)

            logger.info(f"Player {username} successfully joined the game")
            return ConnectionState.PLAY, player

        return ConnectionState.LOGIN, None

    async def _handle_play(self, packet_data: bytes, player: Player):
        """处理游戏状态包"""
        buffer = PacketBuffer(packet_data)
        packet_id = buffer.read_varint()

        if packet_id == 0x12:  # Keep Alive响应
            keep_alive_id = buffer.read_long()
            player.last_keep_alive = time.time()
            logger.debug(f"{player.username}: Keep Alive response {keep_alive_id}")

        elif packet_id == 0x04:  # 聊天消息
            message = buffer.read_string()
            logger.info(f"<{player.username}> {message}")
            # 这里可以添加聊天广播逻辑

        elif packet_id == 0x13:  # 玩家位置
            x = buffer.read_double()
            y = buffer.read_double()
            z = buffer.read_double()
            on_ground = buffer.read_bool()

            player.x, player.y, player.z = x, y, z
            player.on_ground = on_ground
            logger.debug(f"{player.username}: Position update ({x:.2f}, {y:.2f}, {z:.2f})")

        elif packet_id == 0x14:  # 玩家位置和旋转
            x = buffer.read_double()
            y = buffer.read_double()
            z = buffer.read_double()
            yaw = buffer.read_float()
            pitch = buffer.read_float()
            on_ground = buffer.read_bool()

            player.x, player.y, player.z = x, y, z
            player.yaw, player.pitch = yaw, pitch
            player.on_ground = on_ground
            logger.debug(f"{player.username}: Position and rotation update")

        else:
            logger.debug(f"{player.username}: Received play packet 0x{packet_id:02X}")

    async def _send_join_game_sequence(self, player: Player):
        """发送加入游戏序列"""
        try:
            # 1. Join Game包
            join_packet = JoinGamePacket(player.entity_id, self.config)
            await player.send_packet(join_packet)

            # 2. 玩家能力
            abilities_packet = PlayerAbilitiesPacket(creative_mode=True)
            await player.send_packet(abilities_packet)

            # 3. 出生点
            spawn_packet = SpawnPositionPacket(0, 64, 0)
            await player.send_packet(spawn_packet)

            # 4. 玩家位置
            position_packet = PlayerPositionAndLookPacket(
                player.x, player.y, player.z, player.yaw, player.pitch, 1
            )
            await player.send_packet(position_packet)

            logger.info(f"Completed join game sequence for {player.username}")

        except Exception as e:
            logger.error(f"Error sending join game sequence to {player.username}: {e}")

    async def _send_packet(self, writer: asyncio.StreamWriter, packet: Packet):
        """发送数据包"""
        try:
            data = packet.get_data()
            writer.write(data)
            await writer.drain()
        except Exception as e:
            logger.debug(f"Failed to send packet: {e}")

    async def _keep_alive_loop(self):
        """Keep Alive循环"""
        while self.running:
            await asyncio.sleep(30)  # 每30秒

            current_time = time.time()
            keep_alive_id = int(current_time * 1000)

            # 向所有玩家发送Keep Alive
            for player in list(self.players.values()):
                if player.is_connected():
                    try:
                        player.keep_alive_id = keep_alive_id
                        keep_alive_packet = KeepAlivePacket(keep_alive_id)
                        await player.send_packet(keep_alive_packet)
                    except Exception as e:
                        logger.debug(f"Failed to send keep alive to {player.username}: {e}")
                else:
                    # 移除断开连接的玩家
                    if player.uuid in self.players:
                        del self.players[player.uuid]

async def main():
    """主函数"""
    print("=" * 70)
    print("Paper-inspired Python Minecraft Server")
    print("=" * 70)
    print("Features:")
    print("- Paper-inspired architecture")
    print("- Proper protocol implementation")
    print("- Modular design")
    print("- High performance async networking")
    print("- Creative mode gameplay")
    print("=" * 70)

    # 创建服务器配置
    config = ServerConfig(
        host='0.0.0.0',
        port=25565,
        max_players=20,
        motd='Paper-inspired Python Minecraft Server',
        view_distance=10,
        simulation_distance=10
    )

    # 创建并启动服务器
    server = PaperInspiredServer(config)

    try:
        await server.start()
    except KeyboardInterrupt:
        print("\nShutting down server...")
    except Exception as e:
        logger.error(f"Server error: {e}")
    finally:
        await server.stop()
        print("Server stopped.")

if __name__ == "__main__":
    asyncio.run(main())
