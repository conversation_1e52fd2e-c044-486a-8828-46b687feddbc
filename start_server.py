#!/usr/bin/env python3
"""
启动脚本 - 带有更多配置选项的服务器启动器
"""

import asyncio
import argparse
import logging
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from server.minecraft_server import MinecraftServer

def setup_logging(log_level: str = "INFO"):
    """设置日志配置"""
    numeric_level = getattr(logging, log_level.upper(), None)
    if not isinstance(numeric_level, int):
        raise ValueError(f'Invalid log level: {log_level}')
    
    # 创建logs目录
    os.makedirs('logs', exist_ok=True)
    
    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # 文件处理器
    file_handler = logging.FileHandler('logs/server.log')
    file_handler.setFormatter(formatter)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(numeric_level)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Python Minecraft Server')
    
    parser.add_argument('--host', default='0.0.0.0', 
                       help='服务器绑定地址 (默认: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=25565,
                       help='服务器端口 (默认: 25565)')
    parser.add_argument('--max-players', type=int, default=20,
                       help='最大玩家数 (默认: 20)')
    parser.add_argument('--motd', default='Python Minecraft Server',
                       help='服务器描述 (默认: Python Minecraft Server)')
    parser.add_argument('--world', default='world',
                       help='世界名称 (默认: world)')
    parser.add_argument('--log-level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别 (默认: INFO)')
    parser.add_argument('--flat-world', action='store_true',
                       help='使用平坦世界生成器')
    
    return parser.parse_args()

async def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志
    setup_logging(args.log_level)
    logger = logging.getLogger(__name__)
    
    # 显示启动信息
    print("=" * 50)
    print("Python Minecraft Server")
    print("=" * 50)
    print(f"服务器地址: {args.host}:{args.port}")
    print(f"最大玩家数: {args.max_players}")
    print(f"世界名称: {args.world}")
    print(f"服务器描述: {args.motd}")
    print(f"日志级别: {args.log_level}")
    print("=" * 50)
    
    # 创建服务器配置
    config = {
        'host': args.host,
        'port': args.port,
        'max_players': args.max_players,
        'motd': args.motd,
        'world_name': args.world,
        'flat_world': args.flat_world
    }
    
    # 创建并启动服务器
    server = MinecraftServer(config)
    
    try:
        logger.info("正在启动Python Minecraft服务器...")
        await server.start()
    except KeyboardInterrupt:
        logger.info("收到停止信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"服务器错误: {e}", exc_info=True)
    finally:
        await server.stop()
        logger.info("服务器已停止")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        sys.exit(1)
