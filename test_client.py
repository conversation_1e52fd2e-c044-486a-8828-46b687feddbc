#!/usr/bin/env python3
"""
简单的测试客户端，用于验证服务器功能
"""

import asyncio
import socket
import struct
import json

class TestClient:
    """简单的测试客户端"""
    
    def __init__(self, host='localhost', port=25565):
        self.host = host
        self.port = port
        self.reader = None
        self.writer = None
    
    async def connect(self):
        """连接到服务器"""
        try:
            self.reader, self.writer = await asyncio.open_connection(self.host, self.port)
            print(f"已连接到服务器 {self.host}:{self.port}")
            return True
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def write_varint(self, value):
        """写入VarInt"""
        data = b''
        while True:
            byte = value & 0x7F
            value >>= 7
            if value != 0:
                byte |= 0x80
            data += bytes([byte])
            if value == 0:
                break
        return data
    
    def write_string(self, text):
        """写入字符串"""
        text_bytes = text.encode('utf-8')
        return self.write_varint(len(text_bytes)) + text_bytes
    
    async def send_packet(self, packet_data):
        """发送数据包"""
        length = self.write_varint(len(packet_data))
        self.writer.write(length + packet_data)
        await self.writer.drain()
    
    async def read_varint(self):
        """读取VarInt"""
        value = 0
        position = 0
        
        while True:
            byte_data = await self.reader.read(1)
            if not byte_data:
                raise ConnectionError("连接已关闭")
            
            byte = byte_data[0]
            value |= (byte & 0x7F) << position
            
            if (byte & 0x80) == 0:
                break
                
            position += 7
            if position >= 32:
                raise ValueError("VarInt过大")
                
        return value
    
    async def read_string(self):
        """读取字符串"""
        length = await self.read_varint()
        data = await self.reader.read(length)
        return data.decode('utf-8')
    
    async def read_packet(self):
        """读取数据包"""
        try:
            length = await self.read_varint()
            data = await self.reader.read(length)
            return data
        except Exception as e:
            print(f"读取数据包失败: {e}")
            return None
    
    async def test_server_status(self):
        """测试服务器状态（ping）"""
        print("测试服务器状态...")
        
        # 发送握手包
        handshake_data = (
            self.write_varint(0) +  # 包ID
            self.write_varint(763) +  # 协议版本 (1.20.1)
            self.write_string(self.host) +  # 服务器地址
            struct.pack('>H', self.port) +  # 端口
            self.write_varint(1)  # 下一个状态 (状态查询)
        )
        
        await self.send_packet(handshake_data)
        print("已发送握手包")
        
        # 发送状态请求包
        status_request = self.write_varint(0)  # 包ID
        await self.send_packet(status_request)
        print("已发送状态请求包")
        
        # 读取状态响应
        response_data = await self.read_packet()
        if response_data:
            # 解析响应
            packet_id = response_data[0]
            if packet_id == 0:
                # 读取JSON响应
                json_length = 0
                pos = 1
                while pos < len(response_data) and (response_data[pos-1] & 0x80):
                    pos += 1
                
                # 简单解析（实际应该正确解析VarInt）
                json_data = response_data[pos:].decode('utf-8', errors='ignore')
                try:
                    status = json.loads(json_data)
                    print("服务器状态:")
                    print(f"  版本: {status.get('version', {}).get('name', 'Unknown')}")
                    print(f"  协议: {status.get('version', {}).get('protocol', 'Unknown')}")
                    print(f"  玩家: {status.get('players', {}).get('online', 0)}/{status.get('players', {}).get('max', 0)}")
                    print(f"  描述: {status.get('description', {}).get('text', 'No description')}")
                    return True
                except json.JSONDecodeError:
                    print("无法解析服务器状态JSON")
                    print(f"原始数据: {json_data[:100]}...")
        
        return False
    
    async def disconnect(self):
        """断开连接"""
        if self.writer:
            self.writer.close()
            await self.writer.wait_closed()
        print("已断开连接")

async def main():
    """主测试函数"""
    print("Python Minecraft Server 测试客户端")
    print("=" * 40)
    
    client = TestClient()
    
    # 测试连接
    if await client.connect():
        # 测试服务器状态
        success = await client.test_server_status()
        
        if success:
            print("\n✅ 服务器测试成功！")
            print("服务器正在正常运行，可以使用Minecraft客户端连接。")
        else:
            print("\n❌ 服务器状态测试失败")
        
        await client.disconnect()
    else:
        print("❌ 无法连接到服务器")
        print("请确保服务器正在运行")

if __name__ == "__main__":
    asyncio.run(main())
