#!/usr/bin/env python3
"""
Python Minecraft Server
A basic Minecraft Java Edition server implementation in Python
"""

import asyncio
import logging
from server.minecraft_server import MinecraftServer

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

async def main():
    """Main entry point for the Minecraft server"""
    logger.info("Starting Python Minecraft Server...")
    
    # Server configuration
    config = {
        'host': '0.0.0.0',
        'port': 25565,
        'max_players': 20,
        'motd': 'Python Minecraft Server',
        'world_name': 'world'
    }
    
    # Create and start the server
    server = MinecraftServer(config)
    
    try:
        await server.start()
    except KeyboardInterrupt:
        logger.info("Server shutdown requested...")
    except Exception as e:
        logger.error(f"Server error: {e}")
    finally:
        await server.stop()
        logger.info("Server stopped.")

if __name__ == "__main__":
    asyncio.run(main())
