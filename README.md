# Python Minecraft Server

一个用Python实现的Minecraft Java版服务端，支持基本的游戏功能。

## 功能特性

- ✅ **网络协议支持** - 实现Minecraft Java版网络协议
- ✅ **玩家管理** - 支持多玩家连接、登录、聊天
- ✅ **世界生成** - 基础地形生成（平坦世界）
- ✅ **方块系统** - 支持方块放置和破坏
- ✅ **物理引擎** - 重力、碰撞检测
- ✅ **区块管理** - 动态区块加载和保存
- ✅ **游戏循环** - 20 TPS服务器循环

## 系统要求

- Python 3.8+
- 支持的Minecraft版本: 1.20.1 (协议版本 763)

## 安装和运行

1. **克隆或下载项目**
   ```bash
   # 如果使用git
   git clone <repository-url>
   cd python-minecraft-server
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **启动服务器**
   ```bash
   python main.py
   ```

4. **连接到服务器**
   - 打开Minecraft Java版客户端
   - 选择"多人游戏"
   - 添加服务器: `localhost:25565`
   - 连接并开始游戏！

## 配置

服务器配置在 `main.py` 中：

```python
config = {
    'host': '0.0.0.0',        # 服务器地址
    'port': 25565,            # 服务器端口
    'max_players': 20,        # 最大玩家数
    'motd': 'Python Minecraft Server',  # 服务器描述
    'world_name': 'world'     # 世界名称
}
```

## 项目结构

```
python-minecraft-server/
├── main.py                 # 服务器入口点
├── requirements.txt        # Python依赖
├── server/                 # 服务器核心代码
│   ├── minecraft_server.py # 主服务器类
│   ├── network/           # 网络协议处理
│   │   ├── packet.py      # 数据包处理
│   │   ├── protocol.py    # 协议定义
│   │   └── connection_handler.py  # 连接处理
│   ├── player/            # 玩家管理
│   │   ├── player.py      # 玩家类
│   │   └── player_manager.py  # 玩家管理器
│   ├── world/             # 世界管理
│   │   ├── chunk.py       # 区块系统
│   │   ├── world_generator.py  # 世界生成
│   │   └── world_manager.py   # 世界管理器
│   └── game/              # 游戏逻辑
│       └── physics.py     # 物理引擎
└── worlds/                # 世界存档目录
```

## 支持的功能

### 基础功能
- [x] 玩家连接和断开
- [x] 聊天系统
- [x] 玩家移动和位置同步
- [x] 方块放置和破坏
- [x] 重力和碰撞检测
- [x] 世界保存和加载

### 游戏机制
- [x] 创造模式
- [x] 平坦世界生成
- [x] 基础物理（重力、碰撞）
- [x] 虚空传送到出生点
- [x] Keep-alive机制
- [x] 时间同步

### 限制和已知问题
- 仅支持平坦世界生成
- 没有生物系统
- 没有红石系统
- 没有物品系统
- 简化的物理引擎
- 没有权限系统

## 开发和扩展

### 添加新方块类型
在 `server/game/physics.py` 中的 `solid_blocks` 集合中添加方块ID。

### 修改世界生成
编辑 `server/world/world_generator.py` 中的生成算法。

### 添加新的数据包
1. 在 `server/network/protocol.py` 中定义数据包类
2. 在 `server/network/connection_handler.py` 中添加处理逻辑

## 性能优化建议

- 调整 `max_loaded_chunks` 以控制内存使用
- 修改区块卸载距离 `chunk_unload_distance`
- 优化数据包发送频率
- 使用更高效的数据结构存储方块数据

## 故障排除

### 连接问题
- 确保防火墙允许端口25565
- 检查服务器日志中的错误信息
- 验证Minecraft客户端版本兼容性

### 性能问题
- 监控服务器TPS（目标20 TPS）
- 检查内存使用情况
- 减少同时加载的区块数量

## 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 许可证

本项目仅供学习和研究使用。

## 致谢

- Minecraft协议文档: https://wiki.vg/Protocol
- Python异步编程文档
- Minecraft服务器开发社区
