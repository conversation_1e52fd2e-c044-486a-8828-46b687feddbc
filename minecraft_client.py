#!/usr/bin/env python3
"""
Minecraft客户端
用于测试我们的Python Minecraft服务器
实现完整的Minecraft协议通信
"""

import asyncio
import struct
import json
import logging
import time
import uuid

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MinecraftClient:
    """Minecraft客户端"""
    
    def __init__(self, host='localhost', port=25565, username='TestPlayer'):
        self.host = host
        self.port = port
        self.username = username
        self.reader = None
        self.writer = None
        self.state = 0  # 0=握手, 1=状态, 2=登录, 3=游戏
        self.running = False
        
    async def connect(self):
        """连接到服务器"""
        logger.info(f"连接到Minecraft服务器 {self.host}:{self.port}")
        
        try:
            self.reader, self.writer = await asyncio.open_connection(
                self.host, self.port
            )
            logger.info("成功连接到服务器")
            return True
        except Exception as e:
            logger.error(f"连接失败: {e}")
            return False
    
    async def disconnect(self):
        """断开连接"""
        if self.writer:
            self.writer.close()
            await self.writer.wait_closed()
        logger.info("已断开连接")
    
    async def test_server_status(self):
        """测试服务器状态"""
        logger.info("测试服务器状态...")
        
        # 发送握手包 (状态查询)
        await self.send_handshake(1)
        self.state = 1
        
        # 发送状态请求
        await self.send_status_request()
        
        # 读取状态响应
        response = await self.read_packet()
        if response:
            packet_id = response[0]
            if packet_id == 0x00:
                status_json = self.read_string_from_bytes(response, 1)[0]
                status = json.loads(status_json)
                logger.info(f"服务器状态: {status}")
                return True
        
        return False
    
    async def login_and_play(self):
        """登录并进入游戏"""
        logger.info(f"玩家 {self.username} 尝试登录...")
        
        # 发送握手包 (登录)
        await self.send_handshake(2)
        self.state = 2
        
        # 发送登录开始
        await self.send_login_start()
        
        # 处理登录响应
        while self.state == 2:
            packet = await self.read_packet()
            if packet is None:
                logger.error("登录时连接断开")
                return False
            
            packet_id = packet[0]
            
            if packet_id == 0x02:  # 登录成功
                logger.info("登录成功！")
                self.state = 3
                break
            elif packet_id == 0x00:  # 断开连接
                reason = self.read_string_from_bytes(packet, 1)[0]
                logger.error(f"登录被拒绝: {reason}")
                return False
        
        # 进入游戏状态
        if self.state == 3:
            logger.info("进入游戏状态")
            await self.handle_game_packets()
            return True
        
        return False
    
    async def handle_game_packets(self):
        """处理游戏包"""
        self.running = True
        packet_count = 0
        start_time = time.time()
        
        # 启动Keep Alive响应任务
        asyncio.create_task(self.keep_alive_responder())
        
        logger.info("开始接收游戏包...")
        
        while self.running and packet_count < 100:  # 限制接收包数量
            try:
                packet = await asyncio.wait_for(self.read_packet(), timeout=5.0)
                if packet is None:
                    logger.warning("接收到空包，连接可能断开")
                    break
                
                packet_id = packet[0]
                packet_count += 1
                
                if packet_id == 0x28:  # Join Game
                    logger.info("✅ 收到Join Game包")
                    await self.handle_join_game(packet)
                    
                elif packet_id == 0x34:  # Player Abilities
                    logger.info("✅ 收到Player Abilities包")
                    
                elif packet_id == 0x50:  # Spawn Position
                    logger.info("✅ 收到Spawn Position包")
                    
                elif packet_id == 0x3C:  # Player Position and Look
                    logger.info("✅ 收到Player Position包")
                    await self.send_teleport_confirm(1)  # 确认传送
                    
                elif packet_id == 0x25:  # Chunk Data
                    logger.info("✅ 收到Chunk Data包")
                    
                elif packet_id == 0x5C:  # Time Update
                    logger.info("✅ 收到Time Update包")
                    
                elif packet_id == 0x21:  # Game State Change
                    logger.info("✅ 收到Game State Change包")
                    
                elif packet_id == 0x24:  # Keep Alive
                    keep_alive_id = struct.unpack('>q', packet[1:9])[0]
                    logger.debug(f"收到Keep Alive: {keep_alive_id}")
                    await self.send_keep_alive_response(keep_alive_id)
                    
                else:
                    logger.debug(f"收到未知包: 0x{packet_id:02X}")
                
            except asyncio.TimeoutError:
                logger.info("5秒内没有收到新包，测试完成")
                break
            except Exception as e:
                logger.error(f"处理游戏包时出错: {e}")
                break
        
        elapsed = time.time() - start_time
        logger.info(f"游戏包处理完成: 收到 {packet_count} 个包，耗时 {elapsed:.2f} 秒")
        
        if packet_count > 10:
            logger.info("🎉 服务器工作正常！玩家成功进入游戏并接收到完整的游戏数据")
        else:
            logger.warning("⚠️ 接收到的包数量较少，可能存在问题")
    
    async def handle_join_game(self, packet):
        """处理Join Game包"""
        try:
            pos = 1
            entity_id = struct.unpack('>i', packet[pos:pos+4])[0]
            pos += 4
            
            hardcore = packet[pos] != 0
            pos += 1
            
            gamemode, pos = self.read_varint_from_bytes(packet, pos)
            
            logger.info(f"Join Game详情: 实体ID={entity_id}, 硬核={hardcore}, 游戏模式={gamemode}")
            
        except Exception as e:
            logger.error(f"解析Join Game包失败: {e}")
    
    async def keep_alive_responder(self):
        """Keep Alive响应器"""
        while self.running:
            await asyncio.sleep(1)
    
    async def send_handshake(self, next_state):
        """发送握手包"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x00))  # 包ID
        packet_data.extend(self.write_varint(763))   # 协议版本 (1.20.1)
        packet_data.extend(self.write_string(self.host))  # 服务器地址
        packet_data.extend(struct.pack('>H', self.port))  # 服务器端口
        packet_data.extend(self.write_varint(next_state)) # 下一状态
        
        await self.send_packet(bytes(packet_data))
        logger.info(f"发送握手包，下一状态: {next_state}")
    
    async def send_status_request(self):
        """发送状态请求"""
        packet_data = self.write_varint(0x00)  # 包ID
        await self.send_packet(packet_data)
        logger.info("发送状态请求")
    
    async def send_login_start(self):
        """发送登录开始"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x00))  # 包ID
        packet_data.extend(self.write_string(self.username))  # 用户名
        
        await self.send_packet(bytes(packet_data))
        logger.info(f"发送登录请求: {self.username}")
    
    async def send_teleport_confirm(self, teleport_id):
        """发送传送确认"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x00))  # 包ID
        packet_data.extend(self.write_varint(teleport_id))
        
        await self.send_packet(bytes(packet_data))
        logger.debug(f"发送传送确认: {teleport_id}")
    
    async def send_keep_alive_response(self, keep_alive_id):
        """发送Keep Alive响应"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x12))  # 包ID
        packet_data.extend(struct.pack('>q', keep_alive_id))
        
        await self.send_packet(bytes(packet_data))
        logger.debug(f"发送Keep Alive响应: {keep_alive_id}")
    
    async def send_chat_message(self, message):
        """发送聊天消息"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x04))  # 包ID
        packet_data.extend(self.write_string(message))
        packet_data.extend(struct.pack('>q', int(time.time() * 1000)))  # 时间戳
        packet_data.extend(struct.pack('>q', 0))  # 盐值
        packet_data.extend(self.write_varint(0))  # 签名长度
        packet_data.append(0)  # 签名消息数量
        
        await self.send_packet(bytes(packet_data))
        logger.info(f"发送聊天消息: {message}")
    
    # 工具方法
    async def read_packet(self):
        """读取数据包"""
        try:
            length = await self.read_varint()
            if length is None or length <= 0:
                return None
            
            data = await self.reader.read(length)
            if len(data) != length:
                return None
            
            return data
        except Exception as e:
            logger.debug(f"读取包失败: {e}")
            return None
    
    async def read_varint(self):
        """读取VarInt"""
        value = 0
        position = 0
        
        for _ in range(5):
            try:
                byte_data = await self.reader.read(1)
                if not byte_data:
                    return None
                
                byte = byte_data[0]
                value |= (byte & 0x7F) << position
                
                if (byte & 0x80) == 0:
                    return value
                
                position += 7
            except:
                return None
        
        return None
    
    def write_varint(self, value):
        """写入VarInt"""
        data = bytearray()
        while True:
            byte = value & 0x7F
            value >>= 7
            if value != 0:
                byte |= 0x80
            data.append(byte)
            if value == 0:
                break
        return bytes(data)
    
    def write_string(self, text):
        """写入字符串"""
        text_bytes = text.encode('utf-8')
        return self.write_varint(len(text_bytes)) + text_bytes
    
    def read_varint_from_bytes(self, data, pos):
        """从字节数组读取VarInt"""
        value = 0
        position = 0
        
        while pos < len(data):
            byte = data[pos]
            pos += 1
            
            value |= (byte & 0x7F) << position
            
            if (byte & 0x80) == 0:
                return value, pos
            
            position += 7
            if position >= 32:
                raise ValueError("VarInt is too big")
        
        raise ValueError("Unexpected end of data")
    
    def read_string_from_bytes(self, data, pos):
        """从字节数组读取字符串"""
        length, pos = self.read_varint_from_bytes(data, pos)
        if pos + length > len(data):
            raise ValueError("String length exceeds data")
        
        text = data[pos:pos + length].decode('utf-8')
        return text, pos + length
    
    async def send_packet(self, packet_data):
        """发送数据包"""
        try:
            length = self.write_varint(len(packet_data))
            self.writer.write(length + packet_data)
            await self.writer.drain()
        except Exception as e:
            logger.error(f"发送包失败: {e}")

async def main():
    """主函数"""
    print("=" * 70)
    print("Minecraft客户端测试工具")
    print("=" * 70)
    print("功能:")
    print("- 测试服务器状态")
    print("- 模拟玩家登录")
    print("- 接收和解析游戏包")
    print("- 验证服务器功能")
    print("=" * 70)
    
    client = MinecraftClient(username="TestBot")
    
    try:
        # 连接到服务器
        if not await client.connect():
            return
        
        # 测试服务器状态
        logger.info("=== 测试1: 服务器状态查询 ===")
        if await client.test_server_status():
            logger.info("✅ 服务器状态查询成功")
        else:
            logger.error("❌ 服务器状态查询失败")
            return
        
        # 断开并重新连接进行登录测试
        await client.disconnect()
        await asyncio.sleep(1)
        
        if not await client.connect():
            return
        
        # 测试登录和游戏
        logger.info("=== 测试2: 玩家登录和游戏 ===")
        if await client.login_and_play():
            logger.info("✅ 玩家登录和游戏测试成功")
        else:
            logger.error("❌ 玩家登录和游戏测试失败")
        
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
    finally:
        await client.disconnect()
        print("\n" + "=" * 70)
        print("测试完成")
        print("=" * 70)

if __name__ == "__main__":
    asyncio.run(main())
