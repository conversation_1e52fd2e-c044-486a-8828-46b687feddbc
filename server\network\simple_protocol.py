"""
简化的Minecraft协议包定义
专注于基本连接功能，避免复杂的NBT数据
"""

from .packet import Packet, PacketBuffer

# Status packets
class StatusResponsePacket(Packet):
    """Status response packet (0x00)"""
    
    def __init__(self, json_response: str):
        super().__init__(0x00)
        self.write_packet_id()
        self.buffer.write_string(json_response)

class PongResponsePacket(Packet):
    """Pong response packet (0x01)"""
    
    def __init__(self, payload: int):
        super().__init__(0x01)
        self.write_packet_id()
        self.buffer.write_long(payload)

# Login packets
class DisconnectPacket(Packet):
    """Disconnect packet (0x00)"""
    
    def __init__(self, reason: str):
        super().__init__(0x00)
        self.write_packet_id()
        self.buffer.write_string(reason)

class LoginSuccessPacket(Packet):
    """Login success packet (0x02)"""
    
    def __init__(self, uuid: str, username: str):
        super().__init__(0x02)
        self.write_packet_id()
        self.buffer.write_uuid(uuid)
        self.buffer.write_string(username)

# Play packets - 极简版本
class SimpleJoinGamePacket(Packet):
    """极简的Join game packet - 只包含必要字段"""
    
    def __init__(self, entity_id: int):
        super().__init__(0x28)  # 1.20.1的正确包ID
        self.write_packet_id()
        
        # Entity ID
        self.buffer.write_int(entity_id)
        
        # Hardcore mode
        self.buffer.write_bool(False)
        
        # Gamemode (Creative)
        self.buffer.write_ubyte(1)
        
        # Previous gamemode
        self.buffer.write_byte(-1)
        
        # World names
        self.buffer.write_varint(1)
        self.buffer.write_string("minecraft:overworld")
        
        # Registry codec - 使用最小的有效NBT
        # 这是一个空的compound tag
        registry_nbt = b'\x0A\x00\x00\x00'  # TAG_Compound with empty name, then TAG_End
        self.buffer.buffer.write(registry_nbt)
        
        # Dimension type - 使用最小的有效NBT
        dimension_nbt = b'\x0A\x00\x00\x00'  # TAG_Compound with empty name, then TAG_End
        self.buffer.buffer.write(dimension_nbt)
        
        # World name
        self.buffer.write_string("minecraft:overworld")
        
        # Hashed seed
        self.buffer.write_long(0)
        
        # Max players
        self.buffer.write_varint(20)
        
        # View distance
        self.buffer.write_varint(8)
        
        # Simulation distance
        self.buffer.write_varint(8)
        
        # Reduced debug info
        self.buffer.write_bool(False)
        
        # Enable respawn screen
        self.buffer.write_bool(True)
        
        # Is debug
        self.buffer.write_bool(False)
        
        # Is flat
        self.buffer.write_bool(True)
        
        # Death location (optional)
        self.buffer.write_bool(False)

class SimplePlayerPositionPacket(Packet):
    """简化的玩家位置包"""
    
    def __init__(self, x: float, y: float, z: float, yaw: float, pitch: float):
        super().__init__(0x3C)  # 1.20.1的正确包ID
        self.write_packet_id()
        
        self.buffer.write_double(x)
        self.buffer.write_double(y)
        self.buffer.write_double(z)
        self.buffer.write_float(yaw)
        self.buffer.write_float(pitch)
        self.buffer.write_ubyte(0)  # Flags
        self.buffer.write_varint(0)  # Teleport ID

class SimpleChatPacket(Packet):
    """简化的聊天包"""
    
    def __init__(self, message: str):
        super().__init__(0x67)  # 1.20.1的聊天包ID
        self.write_packet_id()
        
        # 简单的JSON消息
        chat_json = f'{{"text":"{message}"}}'
        self.buffer.write_string(chat_json)
        self.buffer.write_ubyte(0)  # Position (chat)

class SimpleChunkPacket(Packet):
    """极简的区块数据包"""
    
    def __init__(self, chunk_x: int, chunk_z: int):
        super().__init__(0x25)  # 1.20.1的区块数据包ID
        self.write_packet_id()
        
        # 区块坐标
        self.buffer.write_int(chunk_x)
        self.buffer.write_int(chunk_z)
        
        # Heightmaps - 空的NBT
        self.buffer.buffer.write(b'\x00')  # TAG_End
        
        # 区块数据 - 空区块
        self.buffer.write_varint(0)  # 数据长度为0
        
        # Block entities - 空
        self.buffer.write_varint(0)
        
        # Trust edges
        self.buffer.write_bool(True)
        
        # Sky light mask - 空
        self.buffer.write_varint(0)
        
        # Block light mask - 空
        self.buffer.write_varint(0)
        
        # Empty sky light mask - 空
        self.buffer.write_varint(0)
        
        # Empty block light mask - 空
        self.buffer.write_varint(0)

class KeepAlivePacket(Packet):
    """Keep alive packet"""
    
    def __init__(self, keep_alive_id: int):
        super().__init__(0x24)  # 1.20.1的Keep Alive包ID
        self.write_packet_id()
        self.buffer.write_long(keep_alive_id)

class SpawnPositionPacket(Packet):
    """出生点位置包"""
    
    def __init__(self, x: int, y: int, z: int):
        super().__init__(0x50)  # 1.20.1的出生点包ID
        self.write_packet_id()
        self.buffer.write_position(x, y, z)
        self.buffer.write_float(0.0)  # Angle

class PlayerAbilitiesPacket(Packet):
    """玩家能力包"""
    
    def __init__(self, creative_mode: bool = True):
        super().__init__(0x34)  # 1.20.1的玩家能力包ID
        self.write_packet_id()
        
        flags = 0
        if creative_mode:
            flags |= 0x0F  # 所有创造模式权限
            
        self.buffer.write_ubyte(flags)
        self.buffer.write_float(0.05)  # Flying speed
        self.buffer.write_float(0.1)   # Walking speed
