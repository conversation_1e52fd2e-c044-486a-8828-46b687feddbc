"""
Player class representing a connected player
"""

import asyncio
import time
from typing import Optional

class Player:
    """Represents a connected player"""
    
    _next_entity_id = 1
    
    def __init__(self, username: str, uuid: str, writer: asyncio.StreamWriter):
        self.username = username
        self.uuid = uuid
        self.writer = writer
        self.entity_id = Player._next_entity_id
        Player._next_entity_id += 1
        
        # Position and rotation
        self.x = 0.0
        self.y = 64.0
        self.z = 0.0
        self.yaw = 0.0
        self.pitch = 0.0
        self.on_ground = True
        
        # Player state
        self.health = 20.0
        self.food = 20
        self.saturation = 5.0
        self.experience = 0
        self.level = 0

        # Game mode
        self.gamemode = 1  # Creative

        # Physics properties
        self.velocity_x = 0.0
        self.velocity_y = 0.0
        self.velocity_z = 0.0
        self.fall_distance = 0.0
        self.last_ground_y = y
        
        # Connection info
        self.connected_time = time.time()
        self.last_keep_alive = time.time()
        self.keep_alive_id = 0
        
        # Chunks loaded by this player
        self.loaded_chunks = set()
        
    def get_chunk_coords(self) -> tuple:
        """Get the chunk coordinates the player is currently in"""
        chunk_x = int(self.x) >> 4
        chunk_z = int(self.z) >> 4
        return (chunk_x, chunk_z)
    
    def get_view_distance_chunks(self, view_distance: int = 10) -> set:
        """Get all chunk coordinates within view distance"""
        chunk_x, chunk_z = self.get_chunk_coords()
        chunks = set()
        
        for dx in range(-view_distance, view_distance + 1):
            for dz in range(-view_distance, view_distance + 1):
                chunks.add((chunk_x + dx, chunk_z + dz))
        
        return chunks
    
    def distance_to(self, other_player) -> float:
        """Calculate distance to another player"""
        dx = self.x - other_player.x
        dy = self.y - other_player.y
        dz = self.z - other_player.z
        return (dx*dx + dy*dy + dz*dz) ** 0.5
    
    def is_connected(self) -> bool:
        """Check if the player is still connected"""
        return not self.writer.is_closing()
    
    async def send_packet(self, packet):
        """Send a packet to this player"""
        if self.is_connected():
            try:
                from ..network.packet import send_packet
                await send_packet(self.writer, packet)
            except Exception:
                pass  # Connection likely closed
    
    def __str__(self):
        return f"Player({self.username}, {self.uuid})"
    
    def __repr__(self):
        return self.__str__()
