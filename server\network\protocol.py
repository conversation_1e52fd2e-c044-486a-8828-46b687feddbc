"""
Minecraft protocol packet definitions
Contains specific packet classes for different game states
"""

from .packet import Packet, PacketBuffer

# Status packets
class StatusResponsePacket(Packet):
    """Status response packet (0x00)"""
    
    def __init__(self, json_response: str):
        super().__init__(0x00)
        self.write_packet_id()
        self.buffer.write_string(json_response)

class PongResponsePacket(Packet):
    """Pong response packet (0x01)"""
    
    def __init__(self, payload: int):
        super().__init__(0x01)
        self.write_packet_id()
        self.buffer.write_long(payload)

# Login packets
class DisconnectPacket(Packet):
    """Disconnect packet (0x00)"""
    
    def __init__(self, reason: str):
        super().__init__(0x00)
        self.write_packet_id()
        self.buffer.write_string(reason)

class LoginSuccessPacket(Packet):
    """Login success packet (0x02)"""
    
    def __init__(self, uuid: str, username: str):
        super().__init__(0x02)
        self.write_packet_id()
        self.buffer.write_uuid(uuid)
        self.buffer.write_string(username)

# Play packets
class JoinGamePacket(Packet):
    """Join game packet (0x26) - Ultra simplified version"""

    def __init__(self, entity_id: int, gamemode: int, dimension: int, world_name: str, spawn_x: int, spawn_y: int, spawn_z: int):
        super().__init__(0x26)
        self.write_packet_id()

        # Entity ID
        self.buffer.write_int(entity_id)

        # Hardcore mode
        self.buffer.write_bool(False)

        # Gamemode
        self.buffer.write_ubyte(gamemode)

        # Previous gamemode
        self.buffer.write_byte(-1)

        # World names array
        self.buffer.write_varint(1)  # Number of worlds
        self.buffer.write_string("minecraft:overworld")

        # Registry codec (minimal NBT - just the end tag)
        self.buffer.buffer.write(b'\x00')  # TAG_End

        # Dimension type (minimal NBT - just the end tag)
        self.buffer.buffer.write(b'\x00')  # TAG_End

        # World name
        self.buffer.write_string("minecraft:overworld")

        # Hashed seed
        self.buffer.write_long(0)

        # Max players
        self.buffer.write_varint(20)

        # View distance
        self.buffer.write_varint(10)

        # Simulation distance
        self.buffer.write_varint(10)

        # Reduced debug info
        self.buffer.write_bool(False)

        # Enable respawn screen
        self.buffer.write_bool(True)

        # Is debug
        self.buffer.write_bool(False)

        # Is flat
        self.buffer.write_bool(True)

        # Death location (optional)
        self.buffer.write_bool(False)

class PlayerPositionAndLookPacket(Packet):
    """Player position and look packet (0x38)"""
    
    def __init__(self, x: float, y: float, z: float, yaw: float, pitch: float):
        super().__init__(0x38)
        self.write_packet_id()
        
        self.buffer.write_double(x)
        self.buffer.write_double(y)
        self.buffer.write_double(z)
        self.buffer.write_float(yaw)
        self.buffer.write_float(pitch)
        self.buffer.write_ubyte(0)  # Flags
        self.buffer.write_varint(0)  # Teleport ID

class ChatMessagePacket(Packet):
    """Chat message packet (0x64)"""
    
    def __init__(self, message: str):
        super().__init__(0x64)
        self.write_packet_id()
        
        # Chat message as JSON
        chat_json = f'{{"text":"{message}"}}'
        self.buffer.write_string(chat_json)
        self.buffer.write_ubyte(0)  # Position (chat)

class BlockChangePacket(Packet):
    """Block change packet (0x0C)"""
    
    def __init__(self, x: int, y: int, z: int, block_id: int):
        super().__init__(0x0C)
        self.write_packet_id()
        
        self.buffer.write_position(x, y, z)
        self.buffer.write_varint(block_id)

class ChunkDataPacket(Packet):
    """Chunk data packet (0x22)"""
    
    def __init__(self, chunk_x: int, chunk_z: int, chunk_data: bytes):
        super().__init__(0x22)
        self.write_packet_id()
        
        self.buffer.write_int(chunk_x)
        self.buffer.write_int(chunk_z)
        
        # Heightmaps (simplified)
        heightmaps = b'\x0A\x00\x00\x09\x00\x0DMOTION_BLOCKING\x0C\x00\x00\x01\x00\x00\x00\x00\x00\x00\x00\x00\x00'
        self.buffer.buffer.write(heightmaps)
        
        # Chunk data
        self.buffer.write_varint(len(chunk_data))
        self.buffer.buffer.write(chunk_data)
        
        # Block entities (empty)
        self.buffer.write_varint(0)
        
        # Trust edges
        self.buffer.write_bool(True)
        
        # Sky light mask
        self.buffer.write_varint(0)
        
        # Block light mask
        self.buffer.write_varint(0)
        
        # Empty sky light mask
        self.buffer.write_varint(0)
        
        # Empty block light mask
        self.buffer.write_varint(0)

class KeepAlivePacket(Packet):
    """Keep alive packet (0x21)"""
    
    def __init__(self, keep_alive_id: int):
        super().__init__(0x21)
        self.write_packet_id()
        self.buffer.write_long(keep_alive_id)

class TimeUpdatePacket(Packet):
    """Time update packet (0x5C)"""
    
    def __init__(self, world_age: int, time_of_day: int):
        super().__init__(0x5C)
        self.write_packet_id()
        self.buffer.write_long(world_age)
        self.buffer.write_long(time_of_day)

class SpawnPositionPacket(Packet):
    """Spawn position packet (0x4B)"""
    
    def __init__(self, x: int, y: int, z: int):
        super().__init__(0x4B)
        self.write_packet_id()
        self.buffer.write_position(x, y, z)
        self.buffer.write_float(0.0)  # Angle

class PlayerAbilitiesPacket(Packet):
    """Player abilities packet (0x32)"""
    
    def __init__(self, invulnerable: bool = False, flying: bool = False, allow_flying: bool = True, creative_mode: bool = True):
        super().__init__(0x32)
        self.write_packet_id()
        
        flags = 0
        if invulnerable:
            flags |= 0x01
        if flying:
            flags |= 0x02
        if allow_flying:
            flags |= 0x04
        if creative_mode:
            flags |= 0x08
            
        self.buffer.write_ubyte(flags)
        self.buffer.write_float(0.05)  # Flying speed
        self.buffer.write_float(0.1)   # Walking speed
