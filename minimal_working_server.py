#!/usr/bin/env python3
"""
最简化可工作的Minecraft服务器
专注于让玩家能够稳定进入和游玩
"""

import asyncio
import struct
import json
import logging
import uuid
import time

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MinimalWorkingServer:
    """最简化可工作的Minecraft服务器"""
    
    def __init__(self, host='0.0.0.0', port=25565):
        self.host = host
        self.port = port
        self.players = {}
        self.running = False
        
    async def start(self):
        """启动服务器"""
        logger.info(f"启动最简化可工作的Minecraft服务器 {self.host}:{self.port}")
        
        server = await asyncio.start_server(
            self.handle_client, self.host, self.port
        )
        
        self.running = True
        asyncio.create_task(self.keep_alive_loop())
        
        async with server:
            await server.serve_forever()
    
    async def handle_client(self, reader, writer):
        """处理客户端连接"""
        addr = writer.get_extra_info('peername')
        logger.info(f"新客户端连接: {addr}")
        
        try:
            state = 0
            username = None
            
            while True:
                packet_data = await self.read_packet(reader)
                if packet_data is None:
                    break
                
                packet_id = packet_data[0] if packet_data else 0
                
                if state == 0:  # 握手
                    if packet_id == 0x00:
                        # 简单解析握手包
                        try:
                            pos = 1
                            protocol_version, pos = self.read_varint_from_bytes(packet_data, pos)
                            server_address, pos = self.read_string_from_bytes(packet_data, pos)
                            server_port = struct.unpack('>H', packet_data[pos:pos+2])[0]
                            pos += 2
                            next_state, pos = self.read_varint_from_bytes(packet_data, pos)
                            
                            logger.info(f"握手: 协议={protocol_version}, 下一状态={next_state}")
                            state = next_state
                        except:
                            logger.warning("握手包解析失败，使用默认状态")
                            state = 1
                
                elif state == 1:  # 状态查询
                    if packet_id == 0x00:
                        await self.send_status_response(writer)
                    elif packet_id == 0x01:
                        if len(packet_data) >= 9:
                            payload = struct.unpack('>q', packet_data[1:9])[0]
                            await self.send_pong(writer, payload)
                
                elif state == 2:  # 登录
                    if packet_id == 0x00:
                        try:
                            username, _ = self.read_string_from_bytes(packet_data, 1)
                            logger.info(f"玩家 {username} 尝试登录")
                            
                            # 发送登录成功
                            player_uuid = str(uuid.uuid4())
                            await self.send_login_success(writer, player_uuid, username)
                            
                            # 发送最简化的游戏初始化
                            await self.send_minimal_game_init(writer, username)
                            
                            state = 3
                            self.players[addr] = {
                                'username': username,
                                'uuid': player_uuid,
                                'writer': writer,
                                'last_keep_alive': time.time()
                            }
                            logger.info(f"玩家 {username} 成功进入游戏")
                        except Exception as e:
                            logger.error(f"登录处理失败: {e}")
                            break
                
                elif state == 3:  # 游戏
                    await self.handle_play_packet(packet_id, packet_data, username)
                
        except Exception as e:
            logger.error(f"处理客户端 {addr} 时出错: {e}")
        finally:
            if addr in self.players:
                logger.info(f"玩家 {self.players[addr]['username']} 离开游戏")
                del self.players[addr]
            try:
                writer.close()
                await writer.wait_closed()
            except:
                pass
    
    async def send_minimal_game_init(self, writer, username):
        """发送最简化的游戏初始化"""
        try:
            # 1. 最简化的Join Game包
            await self.send_minimal_join_game(writer)
            
            # 2. 玩家能力 (创造模式)
            await self.send_player_abilities(writer)
            
            # 3. 玩家位置 (在空中，避免掉落问题)
            await self.send_player_position(writer)
            
            # 4. 发送一个空区块让玩家有地方站
            await self.send_empty_chunk(writer, 0, 0)
            
            # 5. Keep Alive
            await self.send_keep_alive(writer, 1)
            
            logger.info(f"完成 {username} 的最简化游戏初始化")
            
        except Exception as e:
            logger.error(f"初始化 {username} 时出错: {e}")
    
    async def send_minimal_join_game(self, writer):
        """发送最简化的Join Game包"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x28))  # 包ID
        
        # 实体ID
        packet_data.extend(struct.pack('>i', 1))
        
        # 硬核模式
        packet_data.append(0)
        
        # 游戏模式 (创造模式)
        packet_data.extend(self.write_varint(1))
        
        # 之前的游戏模式
        packet_data.extend(self.write_varint(255))  # -1
        
        # 世界名称列表
        packet_data.extend(self.write_varint(1))
        packet_data.extend(self.write_string("minecraft:overworld"))
        
        # 维度编解码器 - 使用最简单的空NBT
        packet_data.append(0x00)  # TAG_End (空NBT)
        
        # 维度类型 - 使用最简单的空NBT
        packet_data.append(0x00)  # TAG_End (空NBT)
        
        # 世界名称
        packet_data.extend(self.write_string("minecraft:overworld"))
        
        # 种子哈希
        packet_data.extend(struct.pack('>q', 0))
        
        # 最大玩家数
        packet_data.extend(self.write_varint(20))
        
        # 视距
        packet_data.extend(self.write_varint(2))  # 很小的视距
        
        # 模拟距离
        packet_data.extend(self.write_varint(2))
        
        # 减少调试信息
        packet_data.append(0)
        
        # 启用重生屏幕
        packet_data.append(1)
        
        # 调试模式
        packet_data.append(0)
        
        # 平坦世界
        packet_data.append(1)
        
        # 死亡位置
        packet_data.append(0)
        
        await self.send_packet(writer, bytes(packet_data))
    
    async def send_player_abilities(self, writer):
        """发送玩家能力"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x34))
        
        # 创造模式的所有能力
        packet_data.append(0x0F)
        packet_data.extend(struct.pack('>f', 0.05))  # 飞行速度
        packet_data.extend(struct.pack('>f', 0.1))   # 行走速度
        
        await self.send_packet(writer, bytes(packet_data))
    
    async def send_player_position(self, writer):
        """发送玩家位置"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x3C))
        
        # 位置 (在空中)
        packet_data.extend(struct.pack('>d', 0.5))   # X
        packet_data.extend(struct.pack('>d', 200.0)) # Y (很高，确保在空中)
        packet_data.extend(struct.pack('>d', 0.5))   # Z
        
        # 旋转
        packet_data.extend(struct.pack('>f', 0.0))   # Yaw
        packet_data.extend(struct.pack('>f', 0.0))   # Pitch
        
        # 标志和传送ID
        packet_data.append(0)
        packet_data.extend(self.write_varint(1))
        
        await self.send_packet(writer, bytes(packet_data))
    
    async def send_empty_chunk(self, writer, chunk_x, chunk_z):
        """发送空区块"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x25))  # 包ID
        
        # 区块坐标
        packet_data.extend(struct.pack('>i', chunk_x))
        packet_data.extend(struct.pack('>i', chunk_z))
        
        # 高度图 (空NBT)
        packet_data.append(0x00)  # TAG_End
        
        # 区块数据 (完全空的)
        packet_data.extend(self.write_varint(0))  # 数据长度为0
        
        # 方块实体数量
        packet_data.extend(self.write_varint(0))
        
        # 信任边缘
        packet_data.append(1)
        
        # 光照掩码 (全部为空)
        for _ in range(4):
            packet_data.extend(self.write_varint(0))
        
        await self.send_packet(writer, bytes(packet_data))
    
    async def send_keep_alive(self, writer, keep_alive_id):
        """发送Keep Alive"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x24))
        packet_data.extend(struct.pack('>q', keep_alive_id))
        await self.send_packet(writer, bytes(packet_data))
    
    async def handle_play_packet(self, packet_id, packet_data, username):
        """处理游戏包"""
        if packet_id == 0x12:  # Keep Alive响应
            logger.debug(f"{username}: Keep Alive响应")
        elif packet_id == 0x04:  # 聊天消息
            try:
                message, _ = self.read_string_from_bytes(packet_data, 1)
                logger.info(f"<{username}> {message}")
            except:
                pass
        else:
            logger.debug(f"{username}: 游戏包 0x{packet_id:02X}")
    
    async def keep_alive_loop(self):
        """Keep Alive循环"""
        while self.running:
            await asyncio.sleep(20)  # 每20秒
            
            current_time = time.time()
            keep_alive_id = int(current_time * 1000)
            
            for addr, player_info in list(self.players.items()):
                try:
                    writer = player_info['writer']
                    await self.send_keep_alive(writer, keep_alive_id)
                    player_info['last_keep_alive'] = current_time
                except:
                    if addr in self.players:
                        del self.players[addr]

    # 工具方法
    async def read_packet(self, reader):
        """读取数据包"""
        try:
            length = await self.read_varint(reader)
            if length is None or length <= 0:
                return None
            data = await reader.read(length)
            if len(data) != length:
                return None
            return data
        except:
            return None

    async def read_varint(self, reader):
        """读取VarInt"""
        value = 0
        position = 0
        for _ in range(5):
            byte_data = await reader.read(1)
            if not byte_data:
                return None
            byte = byte_data[0]
            value |= (byte & 0x7F) << position
            if (byte & 0x80) == 0:
                return value
            position += 7
        return None

    def write_varint(self, value):
        """写入VarInt"""
        data = bytearray()
        while True:
            byte = value & 0x7F
            value >>= 7
            if value != 0:
                byte |= 0x80
            data.append(byte)
            if value == 0:
                break
        return bytes(data)

    def write_string(self, text):
        """写入字符串"""
        text_bytes = text.encode('utf-8')
        return self.write_varint(len(text_bytes)) + text_bytes

    def read_varint_from_bytes(self, data, pos):
        """从字节数组读取VarInt"""
        value = 0
        position = 0

        while pos < len(data):
            byte = data[pos]
            pos += 1

            value |= (byte & 0x7F) << position

            if (byte & 0x80) == 0:
                return value, pos

            position += 7
            if position >= 32:
                raise ValueError("VarInt is too big")

        raise ValueError("Unexpected end of data")

    def read_string_from_bytes(self, data, pos):
        """从字节数组读取字符串"""
        length, pos = self.read_varint_from_bytes(data, pos)
        if pos + length > len(data):
            raise ValueError("String length exceeds data")

        text = data[pos:pos + length].decode('utf-8')
        return text, pos + length

    async def send_packet(self, writer, packet_data):
        """发送数据包"""
        try:
            length = self.write_varint(len(packet_data))
            writer.write(length + packet_data)
            await writer.drain()
        except Exception as e:
            logger.debug(f"发送包时出错: {e}")

    async def send_status_response(self, writer):
        """发送状态响应"""
        status = {
            "version": {"name": "1.20.1", "protocol": 763},
            "players": {"max": 20, "online": len(self.players)},
            "description": {"text": "最简化可工作的Python Minecraft服务器"}
        }
        packet_data = (
            self.write_varint(0x00) +
            self.write_string(json.dumps(status))
        )
        await self.send_packet(writer, packet_data)

    async def send_pong(self, writer, payload):
        """发送Pong响应"""
        packet_data = (
            self.write_varint(0x01) +
            struct.pack('>q', payload)
        )
        await self.send_packet(writer, packet_data)

    async def send_login_success(self, writer, uuid_str, username):
        """发送登录成功"""
        uuid_bytes = b'\x00' * 16  # 简化UUID
        packet_data = (
            self.write_varint(0x02) +
            uuid_bytes +
            self.write_string(username)
        )
        await self.send_packet(writer, packet_data)

async def main():
    """主函数"""
    print("=" * 70)
    print("最简化可工作的Minecraft服务器")
    print("=" * 70)
    print("特点:")
    print("- 极简化实现，专注稳定性")
    print("- 空NBT数据，避免格式错误")
    print("- 创造模式，玩家在空中开始")
    print("- 基础Keep Alive机制")
    print("=" * 70)
    print("使用说明:")
    print("1. 启动服务器")
    print("2. 在Minecraft 1.20.1中连接到 localhost:25565")
    print("3. 你将在空中开始，可以飞行")
    print("4. 这是一个测试服务器，专注于连接稳定性")
    print("=" * 70)

    server = MinimalWorkingServer()

    try:
        await server.start()
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
        server.running = False
    except Exception as e:
        logger.error(f"服务器错误: {e}")
    finally:
        print("服务器已停止")

if __name__ == "__main__":
    asyncio.run(main())
