"""
Player manager for handling multiple connected players
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional

from .player import Player
from ..network.protocol import KeepAlivePacket, TimeUpdatePacket

logger = logging.getLogger(__name__)

class PlayerManager:
    """Manages all connected players"""
    
    def __init__(self, max_players: int):
        self.max_players = max_players
        self.players: Dict[str, Player] = {}  # UUID -> Player
        self.players_by_name: Dict[str, Player] = {}  # Username -> Player
        
        # Keep alive tracking
        self.last_keep_alive = time.time()
        self.keep_alive_interval = 30.0  # seconds
        
        # Time tracking
        self.world_age = 0
        self.time_of_day = 6000  # Start at noon
        
    async def add_player(self, player: Player) -> bool:
        """Add a player to the server"""
        if len(self.players) >= self.max_players:
            return False
        
        if player.uuid in self.players:
            logger.warning(f"Player {player.username} already connected")
            return False
        
        self.players[player.uuid] = player
        self.players_by_name[player.username.lower()] = player
        
        logger.info(f"Player {player.username} joined the game ({len(self.players)}/{self.max_players})")
        
        # Send initial packets
        await self._send_initial_packets(player)
        
        # Broadcast join message
        join_message = f"{player.username} joined the game"
        await self.broadcast_message(join_message)
        
        return True
    
    async def remove_player(self, player: Player):
        """Remove a player from the server"""
        if player.uuid in self.players:
            del self.players[player.uuid]
        
        if player.username.lower() in self.players_by_name:
            del self.players_by_name[player.username.lower()]
        
        logger.info(f"Player {player.username} left the game ({len(self.players)}/{self.max_players})")
        
        # Broadcast leave message
        leave_message = f"{player.username} left the game"
        await self.broadcast_message(leave_message)
    
    def get_player_by_uuid(self, uuid: str) -> Optional[Player]:
        """Get a player by UUID"""
        return self.players.get(uuid)
    
    def get_player_by_name(self, username: str) -> Optional[Player]:
        """Get a player by username"""
        return self.players_by_name.get(username.lower())
    
    def get_all_players(self) -> List[Player]:
        """Get all connected players"""
        return list(self.players.values())
    
    def get_players_in_range(self, x: float, y: float, z: float, range_distance: float) -> List[Player]:
        """Get all players within a certain range of a position"""
        players_in_range = []
        
        for player in self.players.values():
            dx = player.x - x
            dy = player.y - y
            dz = player.z - z
            distance = (dx*dx + dy*dy + dz*dz) ** 0.5
            
            if distance <= range_distance:
                players_in_range.append(player)
        
        return players_in_range
    
    async def broadcast_packet(self, packet, exclude_player: Optional[Player] = None):
        """Broadcast a packet to all players"""
        for player in self.players.values():
            if exclude_player and player.uuid == exclude_player.uuid:
                continue
            
            await player.send_packet(packet)
    
    async def broadcast_message(self, message: str, exclude_player: Optional[Player] = None):
        """Broadcast a chat message to all players"""
        from ..network.protocol import ChatMessagePacket
        
        chat_packet = ChatMessagePacket(message)
        await self.broadcast_packet(chat_packet, exclude_player)
    
    async def tick(self):
        """Update all players (called every server tick)"""
        current_time = time.time()
        
        # Send keep alive packets
        if current_time - self.last_keep_alive >= self.keep_alive_interval:
            await self._send_keep_alive()
            self.last_keep_alive = current_time
        
        # Update world time
        self.world_age += 1
        self.time_of_day = (self.time_of_day + 1) % 24000
        
        # Send time update every 20 ticks (1 second)
        if self.world_age % 20 == 0:
            time_packet = TimeUpdatePacket(self.world_age, self.time_of_day)
            await self.broadcast_packet(time_packet)
        
        # Check for disconnected players
        disconnected_players = []
        for player in self.players.values():
            if not player.is_connected():
                disconnected_players.append(player)
        
        # Remove disconnected players
        for player in disconnected_players:
            await self.remove_player(player)
    
    async def _send_initial_packets(self, player: Player):
        """Send initial packets to a newly joined player"""
        from ..network.protocol import (
            SpawnPositionPacket, PlayerAbilitiesPacket, 
            TimeUpdatePacket
        )
        
        # Send spawn position
        spawn_packet = SpawnPositionPacket(0, 64, 0)
        await player.send_packet(spawn_packet)
        
        # Send player abilities
        abilities_packet = PlayerAbilitiesPacket(
            creative_mode=(player.gamemode == 1),
            allow_flying=(player.gamemode == 1)
        )
        await player.send_packet(abilities_packet)
        
        # Send current time
        time_packet = TimeUpdatePacket(self.world_age, self.time_of_day)
        await player.send_packet(time_packet)
    
    async def _send_keep_alive(self):
        """Send keep alive packets to all players"""
        current_time = int(time.time() * 1000)  # Milliseconds
        
        for player in self.players.values():
            player.keep_alive_id = current_time
            keep_alive_packet = KeepAlivePacket(current_time)
            await player.send_packet(keep_alive_packet)
    
    def get_player_count(self) -> int:
        """Get the number of connected players"""
        return len(self.players)
    
    def is_full(self) -> bool:
        """Check if the server is full"""
        return len(self.players) >= self.max_players
