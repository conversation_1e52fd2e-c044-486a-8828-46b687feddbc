"""
Chunk representation and management
"""

import struct
from typing import Dict, List, Optional

class ChunkSection:
    """Represents a 16x16x16 section of a chunk"""
    
    def __init__(self):
        # Block storage - simplified to just block IDs
        self.blocks = [0] * (16 * 16 * 16)  # 4096 blocks
        self.block_light = bytearray(2048)  # 4 bits per block
        self.sky_light = bytearray(2048)    # 4 bits per block
        
    def get_block(self, x: int, y: int, z: int) -> int:
        """Get block ID at local coordinates"""
        if not (0 <= x < 16 and 0 <= y < 16 and 0 <= z < 16):
            return 0
        
        index = y * 256 + z * 16 + x
        return self.blocks[index]
    
    def set_block(self, x: int, y: int, z: int, block_id: int):
        """Set block ID at local coordinates"""
        if not (0 <= x < 16 and 0 <= y < 16 and 0 <= z < 16):
            return
        
        index = y * 256 + z * 16 + x
        self.blocks[index] = block_id
    
    def is_empty(self) -> bool:
        """Check if this section contains only air blocks"""
        return all(block == 0 for block in self.blocks)

class Chunk:
    """Represents a 16x16 chunk of the world"""
    
    def __init__(self, x: int, z: int):
        self.x = x
        self.z = z
        
        # Chunk sections (Y levels -4 to 19 for 1.18+, simplified to 0-15)
        self.sections: Dict[int, ChunkSection] = {}
        
        # Biome data (simplified)
        self.biomes = [1] * (16 * 16)  # Plains biome
        
        # Height maps
        self.height_map = [64] * (16 * 16)  # Surface height
        
        # Chunk generation status
        self.generated = False
        self.populated = False
        
    def get_section(self, section_y: int) -> Optional[ChunkSection]:
        """Get a chunk section by Y level"""
        return self.sections.get(section_y)
    
    def get_or_create_section(self, section_y: int) -> ChunkSection:
        """Get or create a chunk section"""
        if section_y not in self.sections:
            self.sections[section_y] = ChunkSection()
        return self.sections[section_y]
    
    def get_block(self, x: int, y: int, z: int) -> int:
        """Get block at world coordinates within this chunk"""
        if not (0 <= x < 16 and 0 <= z < 16):
            return 0
        
        section_y = y >> 4  # Divide by 16
        section = self.get_section(section_y)
        
        if section is None:
            return 0
        
        local_y = y & 15  # Modulo 16
        return section.get_block(x, local_y, z)
    
    def set_block(self, x: int, y: int, z: int, block_id: int):
        """Set block at world coordinates within this chunk"""
        if not (0 <= x < 16 and 0 <= z < 16):
            return
        
        section_y = y >> 4
        section = self.get_or_create_section(section_y)
        
        local_y = y & 15
        section.set_block(x, local_y, z, block_id)
        
        # Update height map if necessary
        if block_id != 0:  # Non-air block
            height_index = z * 16 + x
            if y > self.height_map[height_index]:
                self.height_map[height_index] = y
    
    def get_height(self, x: int, z: int) -> int:
        """Get surface height at coordinates"""
        if not (0 <= x < 16 and 0 <= z < 16):
            return 64
        
        return self.height_map[z * 16 + x]
    
    def serialize(self) -> bytes:
        """Serialize chunk data for network transmission"""
        data = bytearray()
        
        # Number of sections
        non_empty_sections = [s for s in self.sections.values() if not s.is_empty()]
        
        # For each section
        for section_y in range(16):  # 0 to 15
            section = self.sections.get(section_y)
            
            if section is None or section.is_empty():
                # Empty section
                data.extend(struct.pack('>H', 0))  # Block count
                continue
            
            # Block count (non-zero for non-empty sections)
            block_count = sum(1 for block in section.blocks if block != 0)
            data.extend(struct.pack('>H', block_count))
            
            # Bits per block (simplified - use 4 bits minimum)
            bits_per_block = max(4, (max(section.blocks) if section.blocks else 0).bit_length())
            data.append(bits_per_block)
            
            # Palette (simplified - direct mapping)
            unique_blocks = list(set(section.blocks))
            data.extend(struct.pack('>B', len(unique_blocks)))  # Palette size
            
            for block_id in unique_blocks:
                data.extend(struct.pack('>H', block_id))
            
            # Block data (simplified)
            # In a real implementation, this would be properly packed
            for block in section.blocks:
                data.extend(struct.pack('>H', block))
        
        # Biome data
        for biome in self.biomes:
            data.extend(struct.pack('>B', biome))
        
        return bytes(data)
    
    def __str__(self):
        return f"Chunk({self.x}, {self.z})"
    
    def __repr__(self):
        return self.__str__()
