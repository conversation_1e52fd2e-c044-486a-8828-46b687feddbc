"""
简化的连接处理器
专注于基本连接功能，避免复杂的协议处理
"""

import asyncio
import logging
import json
import uuid

from .packet import PacketBuffer, read_packet, send_packet
from .simple_protocol import *

logger = logging.getLogger(__name__)

class ConnectionState:
    """连接状态"""
    HANDSHAKING = 0
    STATUS = 1
    LOGIN = 2
    PLAY = 3

class SimpleConnectionHandler:
    """简化的连接处理器"""
    
    def __init__(self, server):
        self.server = server
        
    async def handle_connection(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter):
        """处理客户端连接"""
        client_addr = writer.get_extra_info('peername')
        state = ConnectionState.HANDSHAKING
        player = None
        
        logger.info(f"处理来自 {client_addr} 的连接")
        
        try:
            while True:
                # 读取数据包
                packet_buffer = await read_packet(reader)
                if packet_buffer is None:
                    break
                
                # 根据状态处理数据包
                if state == ConnectionState.HANDSHAKING:
                    state = await self._handle_handshaking(packet_buffer, writer)
                elif state == ConnectionState.STATUS:
                    await self._handle_status(packet_buffer, writer)
                elif state == ConnectionState.LOGIN:
                    result = await self._handle_login(packet_buffer, writer)
                    if isinstance(result, tuple):
                        state, player = result
                elif state == ConnectionState.PLAY:
                    if player:
                        await self._handle_play(packet_buffer, writer, player)
                    else:
                        break
                        
        except Exception as e:
            logger.error(f"处理连接 {client_addr} 时出错: {e}")
        finally:
            if player:
                await self.server.player_manager.remove_player(player)
    
    async def _handle_handshaking(self, packet_buffer: PacketBuffer, writer: asyncio.StreamWriter) -> int:
        """处理握手状态"""
        packet_id = packet_buffer.read_varint()
        
        if packet_id == 0x00:  # 握手包
            protocol_version = packet_buffer.read_varint()
            server_address = packet_buffer.read_string()
            server_port = packet_buffer.read_short()
            next_state = packet_buffer.read_varint()
            
            logger.info(f"握手: 协议={protocol_version}, 地址={server_address}, 端口={server_port}, 下一状态={next_state}")
            
            return next_state
        
        return ConnectionState.HANDSHAKING
    
    async def _handle_status(self, packet_buffer: PacketBuffer, writer: asyncio.StreamWriter):
        """处理状态查询"""
        packet_id = packet_buffer.read_varint()
        
        if packet_id == 0x00:  # 状态请求
            # 发送状态响应
            status_response = {
                "version": {
                    "name": "1.20.1",
                    "protocol": 763
                },
                "players": {
                    "max": self.server.max_players,
                    "online": len(self.server.player_manager.players),
                    "sample": []
                },
                "description": {
                    "text": self.server.motd
                }
            }
            
            packet = StatusResponsePacket(json.dumps(status_response))
            await send_packet(writer, packet)
            
        elif packet_id == 0x01:  # Ping请求
            payload = packet_buffer.read_long()
            
            # 发送Pong响应
            packet = PongResponsePacket(payload)
            await send_packet(writer, packet)
    
    async def _handle_login(self, packet_buffer: PacketBuffer, writer: asyncio.StreamWriter) -> tuple:
        """处理登录"""
        packet_id = packet_buffer.read_varint()
        
        if packet_id == 0x00:  # 登录开始
            username = packet_buffer.read_string()
            
            logger.info(f"玩家 {username} 尝试登录")
            
            # 检查服务器是否已满
            if len(self.server.player_manager.players) >= self.server.max_players:
                packet = DisconnectPacket("服务器已满!")
                await send_packet(writer, packet)
                return ConnectionState.LOGIN, None
            
            # 生成UUID
            player_uuid = str(uuid.uuid4())
            
            # 发送登录成功
            packet = LoginSuccessPacket(player_uuid, username)
            await send_packet(writer, packet)
            
            # 创建玩家对象
            from ..player.player import Player
            player = Player(username, player_uuid, writer)
            
            # 添加玩家到管理器
            await self.server.player_manager.add_player(player)
            
            # 发送游戏加入包
            await self._send_join_game(writer, player)
            
            return ConnectionState.PLAY, player
        
        return ConnectionState.LOGIN, None
    
    async def _handle_play(self, packet_buffer: PacketBuffer, writer: asyncio.StreamWriter, player):
        """处理游戏状态"""
        try:
            packet_id = packet_buffer.read_varint()
            
            # 处理各种游戏包
            if packet_id == 0x04:  # 聊天消息
                message = packet_buffer.read_string()
                await self._handle_chat_message(player, message)
            elif packet_id == 0x13:  # 玩家位置
                x = packet_buffer.read_double()
                y = packet_buffer.read_double()
                z = packet_buffer.read_double()
                on_ground = packet_buffer.read_bool()
                player.x, player.y, player.z = x, y, z
                player.on_ground = on_ground
            elif packet_id == 0x14:  # 玩家位置和旋转
                x = packet_buffer.read_double()
                y = packet_buffer.read_double()
                z = packet_buffer.read_double()
                yaw = packet_buffer.read_float()
                pitch = packet_buffer.read_float()
                on_ground = packet_buffer.read_bool()
                player.x, player.y, player.z = x, y, z
                player.yaw, player.pitch = yaw, pitch
                player.on_ground = on_ground
                
        except Exception as e:
            logger.error(f"处理游戏包时出错: {e}")
    
    async def _send_join_game(self, writer: asyncio.StreamWriter, player):
        """发送加入游戏包"""
        try:
            # 发送加入游戏包
            packet = SimpleJoinGamePacket(player.entity_id)
            await send_packet(writer, packet)
            
            # 发送玩家位置
            packet = SimplePlayerPositionPacket(0.5, 65, 0.5, 0, 0)
            await send_packet(writer, packet)
            
            # 发送出生点
            packet = SpawnPositionPacket(0, 64, 0)
            await send_packet(writer, packet)
            
            # 发送玩家能力
            packet = PlayerAbilitiesPacket(creative_mode=True)
            await send_packet(writer, packet)
            
            # 发送一些基本区块
            await self._send_basic_chunks(writer)
            
            logger.info(f"玩家 {player.username} 成功加入游戏")
            
        except Exception as e:
            logger.error(f"发送加入游戏包时出错: {e}")
    
    async def _send_basic_chunks(self, writer: asyncio.StreamWriter):
        """发送基本区块"""
        try:
            # 发送玩家周围的一些空区块
            for chunk_x in range(-2, 3):
                for chunk_z in range(-2, 3):
                    packet = SimpleChunkPacket(chunk_x, chunk_z)
                    await send_packet(writer, packet)
                    
        except Exception as e:
            logger.error(f"发送区块时出错: {e}")
    
    async def _handle_chat_message(self, player, message: str):
        """处理聊天消息"""
        logger.info(f"<{player.username}> {message}")
        
        # 广播消息给所有玩家
        chat_packet = SimpleChatPacket(f"<{player.username}> {message}")
        await self.server.player_manager.broadcast_packet(chat_packet)
