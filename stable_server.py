#!/usr/bin/env python3
"""
稳定的Minecraft服务器
专注于保持玩家连接稳定，确保能够正常游戏
"""

import asyncio
import struct
import json
import logging
import uuid
import time

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class StableServer:
    """稳定的Minecraft服务器"""
    
    def __init__(self, host='0.0.0.0', port=25565):
        self.host = host
        self.port = port
        self.players = {}
        self.keep_alive_counter = 0
        
    async def start(self):
        """启动服务器"""
        logger.info(f"启动稳定Minecraft服务器 {self.host}:{self.port}")
        
        # 启动Keep Alive任务
        asyncio.create_task(self.keep_alive_loop())
        
        server = await asyncio.start_server(
            self.handle_client, self.host, self.port
        )
        
        async with server:
            await server.serve_forever()
    
    async def keep_alive_loop(self):
        """Keep Alive循环"""
        while True:
            await asyncio.sleep(20)  # 每20秒发送一次
            self.keep_alive_counter += 1
            
            # 向所有玩家发送Keep Alive
            for addr, player_info in list(self.players.items()):
                try:
                    writer = player_info['writer']
                    await self.send_keep_alive(writer, self.keep_alive_counter)
                except:
                    # 连接已断开，移除玩家
                    if addr in self.players:
                        del self.players[addr]
    
    async def handle_client(self, reader, writer):
        """处理客户端连接"""
        addr = writer.get_extra_info('peername')
        logger.info(f"新客户端连接: {addr}")
        
        try:
            state = 0  # 0=握手, 1=状态, 2=登录, 3=游戏
            username = None
            
            while True:
                packet_data = await self.read_packet(reader)
                if packet_data is None:
                    break
                
                packet_id = packet_data[0] if packet_data else 0
                
                if state == 0:  # 握手状态
                    if packet_id == 0x00:
                        next_state = packet_data[-1] if len(packet_data) > 1 else 1
                        state = next_state
                        logger.info(f"握手完成，下一状态: {state}")
                
                elif state == 1:  # 状态查询
                    if packet_id == 0x00:
                        await self.send_status_response(writer)
                    elif packet_id == 0x01:
                        if len(packet_data) >= 9:
                            payload = struct.unpack('>q', packet_data[1:9])[0]
                            await self.send_pong(writer, payload)
                
                elif state == 2:  # 登录状态
                    if packet_id == 0x00:
                        username = self.parse_username(packet_data)
                        logger.info(f"玩家 {username} 尝试登录")
                        
                        player_uuid = str(uuid.uuid4())
                        await self.send_login_success(writer, player_uuid, username)
                        
                        # 发送游戏初始化
                        await self.send_game_init(writer, username)
                        
                        state = 3
                        self.players[addr] = {
                            'username': username,
                            'writer': writer,
                            'last_keep_alive': time.time()
                        }
                        logger.info(f"玩家 {username} 成功进入游戏")
                
                elif state == 3:  # 游戏状态
                    await self.handle_play_packet(packet_id, packet_data, writer, username, addr)
                
        except Exception as e:
            logger.error(f"处理客户端 {addr} 时出错: {e}")
        finally:
            if addr in self.players:
                username = self.players[addr]['username']
                logger.info(f"玩家 {username} 离开游戏")
                del self.players[addr]
            try:
                writer.close()
                await writer.wait_closed()
            except:
                pass
    
    def parse_username(self, packet_data):
        """解析用户名"""
        try:
            pos = 1
            username_length = packet_data[pos]
            pos += 1
            username = packet_data[pos:pos + username_length].decode('utf-8')
            return username
        except:
            return "Unknown"
    
    async def handle_play_packet(self, packet_id, packet_data, writer, username, addr):
        """处理游戏包"""
        try:
            if packet_id == 0x12:  # Keep Alive响应
                if addr in self.players:
                    self.players[addr]['last_keep_alive'] = time.time()
                logger.debug(f"{username}: Keep Alive响应")
                
            elif packet_id == 0x04:  # 聊天消息
                logger.info(f"{username}: 发送聊天消息")
                
            elif packet_id == 0x13:  # 玩家位置
                logger.debug(f"{username}: 位置更新")
                
            elif packet_id == 0x14:  # 玩家位置和旋转
                logger.debug(f"{username}: 位置和旋转更新")
                
            elif packet_id == 0x15:  # 玩家旋转
                logger.debug(f"{username}: 旋转更新")
                
            elif packet_id == 0x1A:  # 玩家能力
                logger.debug(f"{username}: 能力更新")
                
            elif packet_id == 0x2C:  # 使用物品
                logger.debug(f"{username}: 使用物品")
                
            else:
                logger.debug(f"{username}: 游戏包 0x{packet_id:02X}")
                
        except Exception as e:
            logger.error(f"处理 {username} 的游戏包时出错: {e}")
    
    async def send_game_init(self, writer, username):
        """发送游戏初始化序列"""
        try:
            # 1. Join Game
            await self.send_join_game(writer)
            
            # 2. 玩家能力
            await self.send_player_abilities(writer)
            
            # 3. 出生点
            await self.send_spawn_position(writer)
            
            # 4. 玩家位置
            await self.send_player_position(writer)
            
            # 5. 发送一个简单的平坦区块
            await self.send_flat_chunk(writer, 0, 0)
            
            # 6. 时间更新
            await self.send_time_update(writer)
            
            # 7. 初始Keep Alive
            await self.send_keep_alive(writer, 1)
            
            logger.info(f"完成 {username} 的游戏初始化")
            
        except Exception as e:
            logger.error(f"初始化 {username} 时出错: {e}")
    
    async def send_join_game(self, writer):
        """发送Join Game包 - 简化版本"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x28))  # 包ID
        
        packet_data.extend(struct.pack('>i', 1))  # 实体ID
        packet_data.append(0)  # 硬核模式
        packet_data.extend(self.write_varint(1))  # 游戏模式 (创造)
        packet_data.extend(self.write_varint(255))  # 之前的游戏模式
        
        # 世界列表
        packet_data.extend(self.write_varint(1))
        packet_data.extend(self.write_string("minecraft:overworld"))
        
        # 简化的NBT数据
        packet_data.append(0x00)  # 注册表编解码器 (TAG_End)
        packet_data.append(0x00)  # 维度类型 (TAG_End)
        
        packet_data.extend(self.write_string("minecraft:overworld"))  # 世界名称
        packet_data.extend(struct.pack('>q', 0))  # 种子
        packet_data.extend(self.write_varint(20))  # 最大玩家数
        packet_data.extend(self.write_varint(8))  # 视距
        packet_data.extend(self.write_varint(8))  # 模拟距离
        packet_data.append(0)  # 减少调试信息
        packet_data.append(1)  # 启用重生屏幕
        packet_data.append(0)  # 调试模式
        packet_data.append(1)  # 平坦世界
        packet_data.append(0)  # 死亡位置
        
        await self.send_packet(writer, bytes(packet_data))
    
    async def send_player_abilities(self, writer):
        """发送玩家能力"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x34))
        packet_data.append(0x0F)  # 所有创造模式能力
        packet_data.extend(struct.pack('>f', 0.05))  # 飞行速度
        packet_data.extend(struct.pack('>f', 0.1))   # 行走速度
        await self.send_packet(writer, bytes(packet_data))
    
    async def send_spawn_position(self, writer):
        """发送出生点"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x50))
        # 位置编码
        position = (0 << 38) | (64 << 26) | 0
        packet_data.extend(struct.pack('>q', position))
        packet_data.extend(struct.pack('>f', 0.0))  # 角度
        await self.send_packet(writer, bytes(packet_data))
    
    async def send_player_position(self, writer):
        """发送玩家位置"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x3C))
        packet_data.extend(struct.pack('>d', 0.5))   # X
        packet_data.extend(struct.pack('>d', 65.0))  # Y
        packet_data.extend(struct.pack('>d', 0.5))   # Z
        packet_data.extend(struct.pack('>f', 0.0))   # Yaw
        packet_data.extend(struct.pack('>f', 0.0))   # Pitch
        packet_data.append(0)  # 标志
        packet_data.extend(self.write_varint(0))     # 传送ID
        await self.send_packet(writer, bytes(packet_data))
    
    async def send_flat_chunk(self, writer, chunk_x, chunk_z):
        """发送平坦区块"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x25))  # 包ID
        
        packet_data.extend(struct.pack('>i', chunk_x))
        packet_data.extend(struct.pack('>i', chunk_z))
        
        # 高度图 (简化)
        packet_data.append(0x00)  # TAG_End
        
        # 创建简单的平坦区块数据
        chunk_data = self.create_flat_chunk_data()
        packet_data.extend(self.write_varint(len(chunk_data)))
        packet_data.extend(chunk_data)
        
        packet_data.extend(self.write_varint(0))  # 方块实体
        packet_data.append(1)  # 信任边缘
        
        # 光照掩码
        for _ in range(4):
            packet_data.extend(self.write_varint(0))
        
        await self.send_packet(writer, bytes(packet_data))
    
    def create_flat_chunk_data(self):
        """创建平坦区块数据"""
        # 极简的区块数据 - 只有空气
        data = bytearray()
        
        # 区块段数量
        data.extend(self.write_varint(1))  # 1个段
        
        # 段0 (Y 0-15)
        data.extend(struct.pack('>h', 0))  # 非空方块数量
        data.append(4)  # 每方块位数
        data.extend(self.write_varint(1))  # 调色板大小
        data.extend(self.write_varint(0))  # 空气方块ID
        data.extend(self.write_varint(0))  # 数据数组长度
        
        return bytes(data)
    
    async def send_time_update(self, writer):
        """发送时间更新"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x5C))
        packet_data.extend(struct.pack('>q', 0))     # 世界年龄
        packet_data.extend(struct.pack('>q', 6000))  # 时间 (正午)
        await self.send_packet(writer, bytes(packet_data))
    
    async def send_keep_alive(self, writer, keep_alive_id):
        """发送Keep Alive"""
        packet_data = bytearray()
        packet_data.extend(self.write_varint(0x24))
        packet_data.extend(struct.pack('>q', keep_alive_id))
        await self.send_packet(writer, bytes(packet_data))
    
    # 基础工具方法
    async def read_packet(self, reader):
        try:
            length = await self.read_varint(reader)
            if length is None or length <= 0:
                return None
            data = await reader.read(length)
            if len(data) != length:
                return None
            return data
        except:
            return None
    
    async def read_varint(self, reader):
        value = 0
        position = 0
        for _ in range(5):
            byte_data = await reader.read(1)
            if not byte_data:
                return None
            byte = byte_data[0]
            value |= (byte & 0x7F) << position
            if (byte & 0x80) == 0:
                return value
            position += 7
        return None
    
    def write_varint(self, value):
        data = bytearray()
        while True:
            byte = value & 0x7F
            value >>= 7
            if value != 0:
                byte |= 0x80
            data.append(byte)
            if value == 0:
                break
        return bytes(data)
    
    def write_string(self, text):
        text_bytes = text.encode('utf-8')
        return self.write_varint(len(text_bytes)) + text_bytes
    
    async def send_packet(self, writer, packet_data):
        try:
            length = self.write_varint(len(packet_data))
            writer.write(length + packet_data)
            await writer.drain()
        except:
            pass  # 连接可能已关闭
    
    async def send_status_response(self, writer):
        status = {
            "version": {"name": "1.20.1", "protocol": 763},
            "players": {"max": 20, "online": len(self.players)},
            "description": {"text": "稳定Python Minecraft服务器\n支持基本游戏功能"}
        }
        packet_data = (
            self.write_varint(0x00) +
            self.write_string(json.dumps(status))
        )
        await self.send_packet(writer, packet_data)
    
    async def send_pong(self, writer, payload):
        packet_data = (
            self.write_varint(0x01) +
            struct.pack('>q', payload)
        )
        await self.send_packet(writer, packet_data)
    
    async def send_login_success(self, writer, uuid_str, username):
        uuid_bytes = b'\x00' * 16
        packet_data = (
            self.write_varint(0x02) +
            uuid_bytes +
            self.write_string(username)
        )
        await self.send_packet(writer, packet_data)

async def main():
    print("=" * 60)
    print("稳定Minecraft服务器")
    print("=" * 60)
    print("功能:")
    print("- 稳定的玩家连接")
    print("- 完整的游戏初始化")
    print("- Keep Alive机制")
    print("- 基础区块支持")
    print("- 创造模式游戏")
    print("=" * 60)
    
    server = StableServer()
    try:
        await server.start()
    except KeyboardInterrupt:
        print("\n服务器停止")

if __name__ == "__main__":
    asyncio.run(main())
