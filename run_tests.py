#!/usr/bin/env python3
"""
服务器功能测试套件
"""

import asyncio
import socket
import time
import sys

async def test_server_connection(host='localhost', port=25565):
    """测试服务器连接"""
    print("测试服务器连接...")
    try:
        reader, writer = await asyncio.wait_for(
            asyncio.open_connection(host, port), 
            timeout=5.0
        )
        writer.close()
        await writer.wait_closed()
        print("✅ 服务器连接测试通过")
        return True
    except asyncio.TimeoutError:
        print("❌ 连接超时")
        return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False

async def test_port_availability(port=25565):
    """测试端口是否被占用"""
    print(f"测试端口 {port} 可用性...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        
        if result == 0:
            print(f"✅ 端口 {port} 正在被使用（服务器运行中）")
            return True
        else:
            print(f"❌ 端口 {port} 未被使用（服务器未运行）")
            return False
    except Exception as e:
        print(f"❌ 端口测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("测试项目文件结构...")
    
    required_files = [
        'main.py',
        'server/__init__.py',
        'server/minecraft_server.py',
        'server/network/__init__.py',
        'server/network/packet.py',
        'server/network/protocol.py',
        'server/network/connection_handler.py',
        'server/player/__init__.py',
        'server/player/player.py',
        'server/player/player_manager.py',
        'server/world/__init__.py',
        'server/world/chunk.py',
        'server/world/world_generator.py',
        'server/world/world_manager.py',
        'server/game/__init__.py',
        'server/game/physics.py',
    ]
    
    missing_files = []
    for file_path in required_files:
        try:
            with open(file_path, 'r'):
                pass
        except FileNotFoundError:
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    else:
        print("✅ 所有必需文件都存在")
        return True

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    
    try:
        import server.minecraft_server
        import server.network.packet
        import server.network.protocol
        import server.network.connection_handler
        import server.player.player
        import server.player.player_manager
        import server.world.chunk
        import server.world.world_generator
        import server.world.world_manager
        import server.game.physics
        
        print("✅ 所有模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("Python Minecraft Server 测试套件")
    print("=" * 50)
    
    tests = [
        ("文件结构", test_file_structure),
        ("模块导入", test_imports),
        ("端口可用性", lambda: asyncio.create_task(test_port_availability())),
        ("服务器连接", lambda: asyncio.create_task(test_server_connection())),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if asyncio.iscoroutine(test_func()):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！服务器可以正常使用。")
        print("\n使用说明:")
        print("1. 运行 'python main.py' 启动服务器")
        print("2. 在Minecraft Java版中连接到 localhost:25565")
        print("3. 享受游戏！")
    else:
        print("⚠️  部分测试失败，请检查上述错误信息。")
    
    return passed == total

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
