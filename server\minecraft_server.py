"""
Main Minecraft Server class
Handles server lifecycle, client connections, and core game loop
"""

import asyncio
import logging
import socket
from typing import Dict, List, Optional

from .network.connection_handler import ConnectionHandler
from .world.world_manager import WorldManager
from .player.player_manager import PlayerManager
from .game.physics import PhysicsEngine

logger = logging.getLogger(__name__)

class MinecraftServer:
    """Main Minecraft server class"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.host = config.get('host', '0.0.0.0')
        self.port = config.get('port', 25565)
        self.max_players = config.get('max_players', 20)
        self.motd = config.get('motd', 'Python Minecraft Server')
        
        # Server components
        self.connection_handler = ConnectionHandler(self)
        self.world_manager = WorldManager(config.get('world_name', 'world'))
        self.player_manager = PlayerManager(self.max_players)
        self.physics_engine = None  # Will be initialized after world manager
        
        # Server state
        self.running = False
        self.server_socket = None
        self.clients: List[asyncio.StreamWriter] = []
        
    async def start(self):
        """Start the Minecraft server"""
        logger.info(f"Starting server on {self.host}:{self.port}")
        logger.info(f"MOTD: {self.motd}")
        logger.info(f"Max players: {self.max_players}")
        
        # Initialize world
        await self.world_manager.initialize()

        # Initialize physics engine
        self.physics_engine = PhysicsEngine(self.world_manager)
        
        # Start the server socket
        self.server_socket = await asyncio.start_server(
            self._handle_client,
            self.host,
            self.port
        )
        
        self.running = True
        logger.info("Server started successfully!")
        
        # Start the game loop
        game_loop_task = asyncio.create_task(self._game_loop())
        
        # Wait for the server to be closed
        async with self.server_socket:
            await self.server_socket.serve_forever()
            
    async def stop(self):
        """Stop the Minecraft server"""
        logger.info("Stopping server...")
        self.running = False
        
        # Close all client connections
        for client in self.clients:
            client.close()
            await client.wait_closed()
        
        # Close server socket
        if self.server_socket:
            self.server_socket.close()
            await self.server_socket.wait_closed()
            
        # Save world data
        await self.world_manager.save()
        
    async def _handle_client(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter):
        """Handle new client connection"""
        client_addr = writer.get_extra_info('peername')
        logger.info(f"New client connected: {client_addr}")
        
        self.clients.append(writer)
        
        try:
            await self.connection_handler.handle_connection(reader, writer)
        except Exception as e:
            logger.error(f"Error handling client {client_addr}: {e}")
        finally:
            if writer in self.clients:
                self.clients.remove(writer)
            writer.close()
            await writer.wait_closed()
            logger.info(f"Client disconnected: {client_addr}")
            
    async def _game_loop(self):
        """Main game loop - runs at 20 TPS (50ms per tick)"""
        tick_rate = 20  # ticks per second
        tick_duration = 1.0 / tick_rate
        
        logger.info(f"Starting game loop at {tick_rate} TPS")
        
        while self.running:
            tick_start = asyncio.get_event_loop().time()
            
            try:
                # Update world
                await self.world_manager.tick()
                
                # Update players
                await self.player_manager.tick()

                # Apply physics to all players
                for player in self.player_manager.get_all_players():
                    await self.physics_engine.apply_gravity(player)

                    # Check if player fell into void
                    if await self.physics_engine.check_player_in_void(player):
                        await self.physics_engine.teleport_to_spawn(player, self.world_manager)
                
                # Calculate sleep time to maintain tick rate
                tick_end = asyncio.get_event_loop().time()
                tick_time = tick_end - tick_start
                sleep_time = max(0, tick_duration - tick_time)
                
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                else:
                    logger.warning(f"Server running behind! Tick took {tick_time:.3f}s")
                    
            except Exception as e:
                logger.error(f"Error in game loop: {e}")
                await asyncio.sleep(0.1)  # Prevent tight error loop
