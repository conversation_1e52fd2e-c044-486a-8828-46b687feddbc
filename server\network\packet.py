"""
Minecraft packet handling utilities
Implements packet reading/writing for Minecraft protocol
"""

import struct
import asyncio
import json
from typing import Any, Dict, List, Optional, Union
from io import BytesIO

class PacketBuffer:
    """Buffer for reading/writing Minecraft packets"""
    
    def __init__(self, data: bytes = b''):
        self.buffer = BytesIO(data)
        
    def read_varint(self) -> int:
        """Read a VarInt from the buffer"""
        value = 0
        position = 0
        
        while True:
            byte = self.buffer.read(1)
            if not byte:
                raise ValueError("Unexpected end of buffer while reading VarInt")
            
            byte = byte[0]
            value |= (byte & 0x7F) << position
            
            if (byte & 0x80) == 0:
                break
                
            position += 7
            if position >= 32:
                raise ValueError("VarInt is too big")
                
        return value
    
    def write_varint(self, value: int):
        """Write a VarInt to the buffer"""
        while True:
            byte = value & 0x7F
            value >>= 7
            
            if value != 0:
                byte |= 0x80
                
            self.buffer.write(bytes([byte]))
            
            if value == 0:
                break
    
    def read_string(self) -> str:
        """Read a string from the buffer"""
        length = self.read_varint()
        data = self.buffer.read(length)
        if len(data) != length:
            raise ValueError("Unexpected end of buffer while reading string")
        return data.decode('utf-8')
    
    def write_string(self, value: str):
        """Write a string to the buffer"""
        data = value.encode('utf-8')
        self.write_varint(len(data))
        self.buffer.write(data)
    
    def read_bool(self) -> bool:
        """Read a boolean from the buffer"""
        byte = self.buffer.read(1)
        if not byte:
            raise ValueError("Unexpected end of buffer while reading boolean")
        return byte[0] != 0
    
    def write_bool(self, value: bool):
        """Write a boolean to the buffer"""
        self.buffer.write(bytes([1 if value else 0]))
    
    def read_byte(self) -> int:
        """Read a signed byte from the buffer"""
        byte = self.buffer.read(1)
        if not byte:
            raise ValueError("Unexpected end of buffer while reading byte")
        return struct.unpack('b', byte)[0]
    
    def write_byte(self, value: int):
        """Write a signed byte to the buffer"""
        self.buffer.write(struct.pack('b', value))
    
    def read_ubyte(self) -> int:
        """Read an unsigned byte from the buffer"""
        byte = self.buffer.read(1)
        if not byte:
            raise ValueError("Unexpected end of buffer while reading unsigned byte")
        return byte[0]
    
    def write_ubyte(self, value: int):
        """Write an unsigned byte to the buffer"""
        self.buffer.write(bytes([value]))
    
    def read_short(self) -> int:
        """Read a signed short from the buffer"""
        data = self.buffer.read(2)
        if len(data) != 2:
            raise ValueError("Unexpected end of buffer while reading short")
        return struct.unpack('>h', data)[0]
    
    def write_short(self, value: int):
        """Write a signed short to the buffer"""
        self.buffer.write(struct.pack('>h', value))
    
    def read_int(self) -> int:
        """Read a signed int from the buffer"""
        data = self.buffer.read(4)
        if len(data) != 4:
            raise ValueError("Unexpected end of buffer while reading int")
        return struct.unpack('>i', data)[0]
    
    def write_int(self, value: int):
        """Write a signed int to the buffer"""
        self.buffer.write(struct.pack('>i', value))
    
    def read_long(self) -> int:
        """Read a signed long from the buffer"""
        data = self.buffer.read(8)
        if len(data) != 8:
            raise ValueError("Unexpected end of buffer while reading long")
        return struct.unpack('>q', data)[0]
    
    def write_long(self, value: int):
        """Write a signed long to the buffer"""
        self.buffer.write(struct.pack('>q', value))
    
    def read_float(self) -> float:
        """Read a float from the buffer"""
        data = self.buffer.read(4)
        if len(data) != 4:
            raise ValueError("Unexpected end of buffer while reading float")
        return struct.unpack('>f', data)[0]
    
    def write_float(self, value: float):
        """Write a float to the buffer"""
        self.buffer.write(struct.pack('>f', value))
    
    def read_double(self) -> float:
        """Read a double from the buffer"""
        data = self.buffer.read(8)
        if len(data) != 8:
            raise ValueError("Unexpected end of buffer while reading double")
        return struct.unpack('>d', data)[0]
    
    def write_double(self, value: float):
        """Write a double to the buffer"""
        self.buffer.write(struct.pack('>d', value))
    
    def read_uuid(self) -> str:
        """Read a UUID from the buffer"""
        data = self.buffer.read(16)
        if len(data) != 16:
            raise ValueError("Unexpected end of buffer while reading UUID")
        
        # Convert bytes to UUID string format
        uuid_bytes = struct.unpack('>LLLL', data)
        return f"{uuid_bytes[0]:08x}-{uuid_bytes[1] >> 16:04x}-{uuid_bytes[1] & 0xFFFF:04x}-{uuid_bytes[2] >> 16:04x}-{uuid_bytes[2] & 0xFFFF:04x}{uuid_bytes[3]:08x}"
    
    def write_uuid(self, value: str):
        """Write a UUID to the buffer"""
        # Parse UUID string and convert to bytes
        parts = value.replace('-', '')
        if len(parts) != 32:
            raise ValueError("Invalid UUID format")
        
        uuid_int = int(parts, 16)
        self.buffer.write(struct.pack('>QQ', uuid_int >> 64, uuid_int & 0xFFFFFFFFFFFFFFFF))
    
    def read_position(self) -> tuple:
        """Read a position (x, y, z) from the buffer"""
        data = self.read_long()
        x = data >> 38
        y = (data >> 26) & 0xFFF
        z = data & 0x3FFFFFF
        
        # Convert to signed values
        if x >= 2**25:
            x -= 2**26
        if y >= 2**11:
            y -= 2**12
        if z >= 2**25:
            z -= 2**26
            
        return (x, y, z)
    
    def write_position(self, x: int, y: int, z: int):
        """Write a position (x, y, z) to the buffer"""
        # Ensure values are in valid ranges
        x = x & 0x3FFFFFF
        y = y & 0xFFF
        z = z & 0x3FFFFFF
        
        value = (x << 38) | (y << 26) | z
        self.write_long(value)
    
    def read_remaining(self) -> bytes:
        """Read all remaining bytes from the buffer"""
        return self.buffer.read()
    
    def get_bytes(self) -> bytes:
        """Get all bytes from the buffer"""
        current_pos = self.buffer.tell()
        self.buffer.seek(0)
        data = self.buffer.read()
        self.buffer.seek(current_pos)
        return data
    
    def remaining(self) -> int:
        """Get number of remaining bytes in buffer"""
        current_pos = self.buffer.tell()
        self.buffer.seek(0, 2)  # Seek to end
        end_pos = self.buffer.tell()
        self.buffer.seek(current_pos)
        return end_pos - current_pos


class Packet:
    """Base class for Minecraft packets"""

    def __init__(self, packet_id: int):
        self.packet_id = packet_id
        self.buffer = PacketBuffer()

    def write_packet_id(self):
        """Write the packet ID to the buffer"""
        self.buffer.write_varint(self.packet_id)

    def get_data(self) -> bytes:
        """Get the complete packet data with length prefix"""
        packet_data = self.buffer.get_bytes()

        # Create a new buffer for the complete packet
        complete_buffer = PacketBuffer()
        complete_buffer.write_varint(len(packet_data))
        complete_buffer.buffer.write(packet_data)

        return complete_buffer.get_bytes()


# Packet reading utilities
async def read_packet(reader: asyncio.StreamReader) -> Optional[PacketBuffer]:
    """Read a complete packet from the stream"""
    try:
        # Read packet length
        length_data = b''
        for _ in range(5):  # VarInt can be at most 5 bytes
            byte = await reader.read(1)
            if not byte:
                return None

            length_data += byte
            if (byte[0] & 0x80) == 0:
                break

        # Parse length
        length_buffer = PacketBuffer(length_data)
        packet_length = length_buffer.read_varint()

        if packet_length <= 0:
            return None

        # Read packet data
        packet_data = await reader.read(packet_length)
        if len(packet_data) != packet_length:
            return None

        return PacketBuffer(packet_data)

    except Exception:
        return None


async def send_packet(writer: asyncio.StreamWriter, packet: Packet):
    """Send a packet to the client"""
    try:
        data = packet.get_data()
        writer.write(data)
        await writer.drain()
    except Exception:
        pass  # Connection likely closed
