"""
World generation system
Creates terrain using various algorithms
"""

import random
import math
from typing import Dict, List, Tuple

from .chunk import Chunk, ChunkSection

class WorldGenerator:
    """Generates world terrain"""
    
    def __init__(self, seed: int = None):
        self.seed = seed if seed is not None else random.randint(0, 2**31 - 1)
        self.random = random.Random(self.seed)
        
        # Generation parameters
        self.sea_level = 64
        self.base_height = 64
        self.height_variation = 32
        
        # Noise parameters for terrain generation
        self.terrain_scale = 0.01
        self.height_scale = 0.005
        
    def generate_chunk(self, chunk_x: int, chunk_z: int) -> Chunk:
        """Generate a new chunk"""
        chunk = Chunk(chunk_x, chunk_z)
        
        # Generate terrain
        self._generate_terrain(chunk)
        
        # Add basic structures
        self._add_basic_structures(chunk)
        
        chunk.generated = True
        chunk.populated = True
        
        return chunk
    
    def _generate_terrain(self, chunk: Chunk):
        """Generate basic terrain for the chunk"""
        world_x_offset = chunk.x * 16
        world_z_offset = chunk.z * 16
        
        for local_x in range(16):
            for local_z in range(16):
                world_x = world_x_offset + local_x
                world_z = world_z_offset + local_z
                
                # Generate height using simple noise
                height = self._get_height_at(world_x, world_z)
                
                # Generate terrain layers
                self._generate_column(chunk, local_x, local_z, height)
    
    def _get_height_at(self, x: int, z: int) -> int:
        """Get terrain height at world coordinates"""
        # Simple noise-based height generation
        noise_value = self._simple_noise(x * self.terrain_scale, z * self.terrain_scale)
        height_noise = self._simple_noise(x * self.height_scale, z * self.height_scale)
        
        # Combine noises for varied terrain
        base_height = self.base_height + noise_value * 16
        height_variation = height_noise * self.height_variation
        
        final_height = int(base_height + height_variation)
        return max(1, min(255, final_height))
    
    def _simple_noise(self, x: float, z: float) -> float:
        """Simple noise function for terrain generation"""
        # Use sine waves for simple noise
        return (math.sin(x * 2) + math.sin(z * 3) + math.sin((x + z) * 0.5)) / 3.0
    
    def _generate_column(self, chunk: Chunk, x: int, z: int, height: int):
        """Generate a vertical column of blocks"""
        # Bedrock layer
        chunk.set_block(x, 0, z, 7)  # Bedrock
        
        # Stone layer
        for y in range(1, height - 4):
            chunk.set_block(x, y, z, 1)  # Stone
        
        # Dirt layer
        for y in range(max(1, height - 4), height - 1):
            chunk.set_block(x, y, z, 3)  # Dirt
        
        # Surface layer
        if height >= self.sea_level:
            chunk.set_block(x, height - 1, z, 2)  # Grass
        else:
            chunk.set_block(x, height - 1, z, 12)  # Sand
            
            # Fill with water up to sea level
            for y in range(height, self.sea_level):
                chunk.set_block(x, y, z, 9)  # Water
    
    def _add_basic_structures(self, chunk: Chunk):
        """Add basic structures like trees"""
        world_x_offset = chunk.x * 16
        world_z_offset = chunk.z * 16
        
        # Generate trees randomly
        tree_random = random.Random(self.seed + chunk.x * 1000 + chunk.z)
        
        for _ in range(tree_random.randint(0, 3)):  # 0-3 trees per chunk
            local_x = tree_random.randint(2, 13)
            local_z = tree_random.randint(2, 13)
            
            world_x = world_x_offset + local_x
            world_z = world_z_offset + local_z
            
            height = self._get_height_at(world_x, world_z)
            
            # Only place trees on grass
            if chunk.get_block(local_x, height - 1, local_z) == 2:  # Grass
                self._generate_tree(chunk, local_x, height, local_z)
    
    def _generate_tree(self, chunk: Chunk, x: int, base_y: int, z: int):
        """Generate a simple tree"""
        tree_height = 4 + random.randint(0, 2)
        
        # Tree trunk
        for y in range(base_y, base_y + tree_height):
            chunk.set_block(x, y, z, 17)  # Wood
        
        # Tree leaves
        leaf_y = base_y + tree_height - 1
        
        # Simple leaf pattern
        for dx in range(-2, 3):
            for dz in range(-2, 3):
                for dy in range(0, 3):
                    leaf_x = x + dx
                    leaf_z = z + dz
                    leaf_y_pos = leaf_y + dy
                    
                    # Skip if too far from center or outside chunk
                    if abs(dx) + abs(dz) > 2 or not (0 <= leaf_x < 16 and 0 <= leaf_z < 16):
                        continue
                    
                    # Random chance to place leaf
                    if random.random() < 0.8:
                        chunk.set_block(leaf_x, leaf_y_pos, leaf_z, 18)  # Leaves

class FlatWorldGenerator(WorldGenerator):
    """Generates flat worlds for testing"""
    
    def __init__(self, seed: int = None):
        super().__init__(seed)
        self.flat_height = 4
    
    def _generate_terrain(self, chunk: Chunk):
        """Generate flat terrain"""
        for x in range(16):
            for z in range(16):
                # Bedrock
                chunk.set_block(x, 0, z, 7)
                
                # Dirt layers
                for y in range(1, self.flat_height):
                    chunk.set_block(x, y, z, 3)  # Dirt
                
                # Grass top
                chunk.set_block(x, self.flat_height, z, 2)  # Grass
    
    def _add_basic_structures(self, chunk: Chunk):
        """No structures in flat world"""
        pass

class SuperflatGenerator(WorldGenerator):
    """Generates superflat worlds"""
    
    def __init__(self, seed: int = None, layers: List[Tuple[int, int]] = None):
        super().__init__(seed)
        
        # Default superflat layers: [(block_id, thickness), ...]
        self.layers = layers or [
            (7, 1),   # Bedrock
            (3, 2),   # Dirt
            (2, 1),   # Grass
        ]
    
    def _generate_terrain(self, chunk: Chunk):
        """Generate superflat terrain"""
        current_y = 0
        
        for block_id, thickness in self.layers:
            for y in range(current_y, current_y + thickness):
                for x in range(16):
                    for z in range(16):
                        chunk.set_block(x, y, z, block_id)
            current_y += thickness
    
    def _add_basic_structures(self, chunk: Chunk):
        """Add villages and other structures to superflat"""
        # Could add villages, but keeping simple for now
        pass
