"""
Basic physics system for the Minecraft server
Handles gravity, collision detection, and movement
"""

import math
from typing import Tuple, List

class AABB:
    """Axis-Aligned Bounding Box for collision detection"""
    
    def __init__(self, min_x: float, min_y: float, min_z: float, max_x: float, max_y: float, max_z: float):
        self.min_x = min_x
        self.min_y = min_y
        self.min_z = min_z
        self.max_x = max_x
        self.max_y = max_y
        self.max_z = max_z
    
    def intersects(self, other: 'AABB') -> bool:
        """Check if this AABB intersects with another"""
        return (self.min_x < other.max_x and self.max_x > other.min_x and
                self.min_y < other.max_y and self.max_y > other.min_y and
                self.min_z < other.max_z and self.max_z > other.min_z)
    
    def contains_point(self, x: float, y: float, z: float) -> bool:
        """Check if this AABB contains a point"""
        return (self.min_x <= x <= self.max_x and
                self.min_y <= y <= self.max_y and
                self.min_z <= z <= self.max_z)
    
    def expand(self, dx: float, dy: float, dz: float) -> 'AABB':
        """Expand the AABB by the given amounts"""
        return AABB(
            self.min_x - dx, self.min_y - dy, self.min_z - dz,
            self.max_x + dx, self.max_y + dy, self.max_z + dz
        )

class PhysicsEngine:
    """Basic physics engine for the server"""
    
    def __init__(self, world_manager):
        self.world_manager = world_manager
        
        # Physics constants
        self.gravity = -0.08  # Blocks per tick squared
        self.terminal_velocity = -3.92  # Maximum falling speed
        self.drag = 0.02  # Air resistance
        
        # Player physics
        self.player_width = 0.6
        self.player_height = 1.8
        self.player_eye_height = 1.62
    
    def get_player_aabb(self, x: float, y: float, z: float) -> AABB:
        """Get the bounding box for a player at the given position"""
        half_width = self.player_width / 2
        return AABB(
            x - half_width, y, z - half_width,
            x + half_width, y + self.player_height, z + half_width
        )
    
    def get_block_aabb(self, x: int, y: int, z: int) -> AABB:
        """Get the bounding box for a block"""
        return AABB(x, y, z, x + 1, y + 1, z + 1)
    
    async def is_block_solid(self, x: int, y: int, z: int) -> bool:
        """Check if a block is solid (blocks movement)"""
        block_id = await self.world_manager.get_block(x, y, z)
        
        # Define solid blocks (simplified)
        solid_blocks = {1, 2, 3, 4, 5, 6, 7, 12, 13, 14, 15, 16, 17, 21, 22, 23, 24}
        
        return block_id in solid_blocks
    
    async def get_colliding_blocks(self, aabb: AABB) -> List[Tuple[int, int, int]]:
        """Get all solid blocks that collide with the given AABB"""
        colliding_blocks = []
        
        min_x = int(math.floor(aabb.min_x))
        max_x = int(math.floor(aabb.max_x))
        min_y = int(math.floor(aabb.min_y))
        max_y = int(math.floor(aabb.max_y))
        min_z = int(math.floor(aabb.min_z))
        max_z = int(math.floor(aabb.max_z))
        
        for x in range(min_x, max_x + 1):
            for y in range(min_y, max_y + 1):
                for z in range(min_z, max_z + 1):
                    if await self.is_block_solid(x, y, z):
                        block_aabb = self.get_block_aabb(x, y, z)
                        if aabb.intersects(block_aabb):
                            colliding_blocks.append((x, y, z))
        
        return colliding_blocks
    
    async def move_player(self, player, dx: float, dy: float, dz: float) -> Tuple[float, float, float]:
        """Move a player with collision detection"""
        original_x, original_y, original_z = player.x, player.y, player.z
        
        # Try to move in each axis separately
        new_x, new_y, new_z = original_x, original_y, original_z
        
        # Move X
        if dx != 0:
            test_aabb = self.get_player_aabb(original_x + dx, original_y, original_z)
            colliding_blocks = await self.get_colliding_blocks(test_aabb)
            
            if not colliding_blocks:
                new_x = original_x + dx
        
        # Move Y
        if dy != 0:
            test_aabb = self.get_player_aabb(new_x, original_y + dy, original_z)
            colliding_blocks = await self.get_colliding_blocks(test_aabb)
            
            if not colliding_blocks:
                new_y = original_y + dy
            else:
                # Hit ground or ceiling
                if dy < 0:  # Falling
                    player.on_ground = True
                    player.velocity_y = 0
                elif dy > 0:  # Jumping into ceiling
                    player.velocity_y = 0
        
        # Move Z
        if dz != 0:
            test_aabb = self.get_player_aabb(new_x, new_y, original_z + dz)
            colliding_blocks = await self.get_colliding_blocks(test_aabb)
            
            if not colliding_blocks:
                new_z = original_z + dz
        
        return new_x, new_y, new_z
    
    async def apply_gravity(self, player):
        """Apply gravity to a player"""
        if not player.on_ground and player.gamemode != 1:  # Not creative mode
            # Apply gravity
            if not hasattr(player, 'velocity_y'):
                player.velocity_y = 0
            
            player.velocity_y += self.gravity
            
            # Apply terminal velocity
            if player.velocity_y < self.terminal_velocity:
                player.velocity_y = self.terminal_velocity
            
            # Move player down
            new_x, new_y, new_z = await self.move_player(player, 0, player.velocity_y, 0)
            
            # Check if player hit ground
            if new_y == player.y and player.velocity_y < 0:
                player.on_ground = True
                player.velocity_y = 0
            else:
                player.on_ground = False
            
            player.x, player.y, player.z = new_x, new_y, new_z
    
    async def check_player_in_void(self, player) -> bool:
        """Check if player has fallen into the void"""
        return player.y < -64
    
    async def teleport_to_spawn(self, player, world_manager):
        """Teleport player to spawn point"""
        player.x = world_manager.spawn_x
        player.y = world_manager.spawn_y
        player.z = world_manager.spawn_z
        player.velocity_y = 0
        player.on_ground = True
    
    def calculate_fall_damage(self, fall_distance: float) -> float:
        """Calculate fall damage based on distance"""
        if fall_distance <= 3.0:
            return 0.0
        
        return fall_distance - 3.0
    
    def is_in_water(self, x: float, y: float, z: float) -> bool:
        """Check if a position is in water (simplified)"""
        # This would check for water blocks in a real implementation
        return False
    
    def is_in_lava(self, x: float, y: float, z: float) -> bool:
        """Check if a position is in lava (simplified)"""
        # This would check for lava blocks in a real implementation
        return False
