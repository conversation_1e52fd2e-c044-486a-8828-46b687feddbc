"""
World manager for handling world state and chunk loading
"""

import asyncio
import logging
import os
import pickle
from typing import Dict, List, Optional, Set, Tuple

from .chunk import Chunk
from .world_generator import WorldGenerator, FlatWorldGenerator

logger = logging.getLogger(__name__)

class WorldManager:
    """Manages world state, chunk loading, and world generation"""
    
    def __init__(self, world_name: str):
        self.world_name = world_name
        self.world_folder = f"worlds/{world_name}"
        
        # Loaded chunks
        self.chunks: Dict[Tuple[int, int], Chunk] = {}
        
        # World generator
        self.generator = FlatWorldGenerator()  # Use flat world for simplicity
        
        # World properties
        self.spawn_x = 0
        self.spawn_y = 65
        self.spawn_z = 0
        
        # Chunk loading settings
        self.max_loaded_chunks = 1000
        self.chunk_unload_distance = 15
        
        # World time
        self.world_age = 0
        self.time_of_day = 6000  # Noon
        
    async def initialize(self):
        """Initialize the world manager"""
        logger.info(f"Initializing world: {self.world_name}")
        
        # Create world folder if it doesn't exist
        os.makedirs(self.world_folder, exist_ok=True)
        
        # Load world data if it exists
        await self._load_world_data()
        
        # Generate spawn chunks
        await self._generate_spawn_chunks()
        
        logger.info(f"World {self.world_name} initialized with {len(self.chunks)} chunks loaded")
    
    async def get_chunk(self, chunk_x: int, chunk_z: int) -> Chunk:
        """Get a chunk, loading or generating it if necessary"""
        chunk_coords = (chunk_x, chunk_z)
        
        if chunk_coords in self.chunks:
            return self.chunks[chunk_coords]
        
        # Try to load from disk
        chunk = await self._load_chunk(chunk_x, chunk_z)
        
        if chunk is None:
            # Generate new chunk
            chunk = self.generator.generate_chunk(chunk_x, chunk_z)
            logger.debug(f"Generated new chunk at {chunk_x}, {chunk_z}")
        else:
            logger.debug(f"Loaded chunk from disk at {chunk_x}, {chunk_z}")
        
        # Add to loaded chunks
        self.chunks[chunk_coords] = chunk
        
        # Unload distant chunks if we have too many
        if len(self.chunks) > self.max_loaded_chunks:
            await self._unload_distant_chunks()
        
        return chunk
    
    async def get_block(self, x: int, y: int, z: int) -> int:
        """Get block at world coordinates"""
        chunk_x = x >> 4
        chunk_z = z >> 4
        
        chunk = await self.get_chunk(chunk_x, chunk_z)
        
        local_x = x & 15
        local_z = z & 15
        
        return chunk.get_block(local_x, y, local_z)
    
    async def set_block(self, x: int, y: int, z: int, block_id: int):
        """Set block at world coordinates"""
        chunk_x = x >> 4
        chunk_z = z >> 4
        
        chunk = await self.get_chunk(chunk_x, chunk_z)
        
        local_x = x & 15
        local_z = z & 15
        
        chunk.set_block(local_x, y, local_z, block_id)
        
        # Mark chunk as modified
        chunk.modified = True
    
    async def get_chunks_in_range(self, center_x: int, center_z: int, radius: int) -> List[Chunk]:
        """Get all chunks within a radius"""
        chunks = []
        
        for dx in range(-radius, radius + 1):
            for dz in range(-radius, radius + 1):
                chunk_x = center_x + dx
                chunk_z = center_z + dz
                
                chunk = await self.get_chunk(chunk_x, chunk_z)
                chunks.append(chunk)
        
        return chunks
    
    async def load_chunks_for_player(self, player_x: float, player_z: float, view_distance: int = 10) -> List[Chunk]:
        """Load chunks around a player"""
        center_chunk_x = int(player_x) >> 4
        center_chunk_z = int(player_z) >> 4
        
        return await self.get_chunks_in_range(center_chunk_x, center_chunk_z, view_distance)
    
    async def tick(self):
        """Update world state (called every server tick)"""
        self.world_age += 1
        self.time_of_day = (self.time_of_day + 1) % 24000
        
        # Periodically save chunks
        if self.world_age % 1200 == 0:  # Every minute
            await self._save_modified_chunks()
    
    async def save(self):
        """Save all world data"""
        logger.info(f"Saving world {self.world_name}...")
        
        await self._save_world_data()
        await self._save_all_chunks()
        
        logger.info(f"World {self.world_name} saved")
    
    async def _generate_spawn_chunks(self):
        """Generate chunks around spawn point"""
        spawn_chunk_x = self.spawn_x >> 4
        spawn_chunk_z = self.spawn_z >> 4
        
        # Generate 3x3 chunks around spawn
        for dx in range(-1, 2):
            for dz in range(-1, 2):
                await self.get_chunk(spawn_chunk_x + dx, spawn_chunk_z + dz)
    
    async def _load_world_data(self):
        """Load world metadata"""
        world_data_file = os.path.join(self.world_folder, "world_data.pkl")
        
        if os.path.exists(world_data_file):
            try:
                with open(world_data_file, 'rb') as f:
                    data = pickle.load(f)
                    
                self.spawn_x = data.get('spawn_x', 0)
                self.spawn_y = data.get('spawn_y', 65)
                self.spawn_z = data.get('spawn_z', 0)
                self.world_age = data.get('world_age', 0)
                self.time_of_day = data.get('time_of_day', 6000)
                
                logger.info(f"Loaded world data for {self.world_name}")
            except Exception as e:
                logger.warning(f"Failed to load world data: {e}")
    
    async def _save_world_data(self):
        """Save world metadata"""
        world_data_file = os.path.join(self.world_folder, "world_data.pkl")
        
        data = {
            'spawn_x': self.spawn_x,
            'spawn_y': self.spawn_y,
            'spawn_z': self.spawn_z,
            'world_age': self.world_age,
            'time_of_day': self.time_of_day
        }
        
        try:
            with open(world_data_file, 'wb') as f:
                pickle.dump(data, f)
        except Exception as e:
            logger.error(f"Failed to save world data: {e}")
    
    async def _load_chunk(self, chunk_x: int, chunk_z: int) -> Optional[Chunk]:
        """Load a chunk from disk"""
        chunk_file = os.path.join(self.world_folder, f"chunk_{chunk_x}_{chunk_z}.pkl")
        
        if not os.path.exists(chunk_file):
            return None
        
        try:
            with open(chunk_file, 'rb') as f:
                chunk = pickle.load(f)
                return chunk
        except Exception as e:
            logger.warning(f"Failed to load chunk {chunk_x}, {chunk_z}: {e}")
            return None
    
    async def _save_chunk(self, chunk: Chunk):
        """Save a chunk to disk"""
        chunk_file = os.path.join(self.world_folder, f"chunk_{chunk.x}_{chunk.z}.pkl")
        
        try:
            with open(chunk_file, 'wb') as f:
                pickle.dump(chunk, f)
        except Exception as e:
            logger.error(f"Failed to save chunk {chunk.x}, {chunk.z}: {e}")
    
    async def _save_all_chunks(self):
        """Save all loaded chunks"""
        for chunk in self.chunks.values():
            await self._save_chunk(chunk)
    
    async def _save_modified_chunks(self):
        """Save only modified chunks"""
        for chunk in self.chunks.values():
            if hasattr(chunk, 'modified') and chunk.modified:
                await self._save_chunk(chunk)
                chunk.modified = False
    
    async def _unload_distant_chunks(self):
        """Unload chunks that are far from any players"""
        # This is a simplified implementation
        # In a real server, you'd check distance from all players
        
        chunks_to_unload = []
        
        # For now, just unload oldest chunks if we have too many
        if len(self.chunks) > self.max_loaded_chunks:
            chunk_items = list(self.chunks.items())
            chunks_to_unload = chunk_items[:-self.max_loaded_chunks//2]
        
        for chunk_coords, chunk in chunks_to_unload:
            # Save before unloading
            await self._save_chunk(chunk)
            del self.chunks[chunk_coords]
            
        if chunks_to_unload:
            logger.debug(f"Unloaded {len(chunks_to_unload)} distant chunks")
