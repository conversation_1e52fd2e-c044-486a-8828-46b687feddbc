# Python Minecraft Server 使用指南

## 快速开始

### 1. 启动服务器

**方法一：使用基本启动器**
```bash
python main.py
```

**方法二：使用高级启动器（推荐）**
```bash
python start_server.py
```

**方法三：自定义配置启动**
```bash
python start_server.py --host 0.0.0.0 --port 25565 --max-players 10 --motd "我的Python服务器"
```

### 2. 连接到服务器

1. 打开Minecraft Java版客户端（版本1.20.1）
2. 选择"多人游戏"
3. 点击"添加服务器"
4. 输入服务器信息：
   - 服务器名称：任意名称
   - 服务器地址：`localhost:25565`（本地）或 `你的IP:25565`（远程）
5. 点击"完成"
6. 选择服务器并点击"加入服务器"

## 服务器配置

### 命令行参数

```bash
python start_server.py --help
```

可用参数：
- `--host`: 服务器绑定地址（默认：0.0.0.0）
- `--port`: 服务器端口（默认：25565）
- `--max-players`: 最大玩家数（默认：20）
- `--motd`: 服务器描述（默认：Python Minecraft Server）
- `--world`: 世界名称（默认：world）
- `--log-level`: 日志级别（DEBUG/INFO/WARNING/ERROR）
- `--flat-world`: 使用平坦世界生成器

### 配置示例

```bash
# 启动一个小型服务器
python start_server.py --max-players 5 --motd "小型测试服务器"

# 启动调试模式
python start_server.py --log-level DEBUG

# 启动在特定端口
python start_server.py --port 25566
```

## 游戏功能

### 支持的功能
- ✅ 玩家连接和断开
- ✅ 聊天系统
- ✅ 玩家移动
- ✅ 方块放置和破坏
- ✅ 重力系统
- ✅ 碰撞检测
- ✅ 创造模式
- ✅ 平坦世界生成
- ✅ 世界保存和加载

### 游戏模式
- **创造模式**：默认模式，可以飞行，无限资源
- **平坦世界**：简单的平坦地形，适合建筑

### 基本操作
- **移动**：WASD键移动
- **跳跃**：空格键
- **飞行**：双击空格键（创造模式）
- **放置方块**：右键点击
- **破坏方块**：左键点击
- **聊天**：T键打开聊天框

## 服务器管理

### 查看服务器状态
```bash
python run_tests.py
```

### 服务器日志
- 控制台输出：实时显示服务器状态
- 日志文件：`logs/server.log`（使用start_server.py时）

### 停止服务器
- 按 `Ctrl+C` 优雅关闭服务器
- 服务器会自动保存世界数据

### 世界管理
- 世界文件保存在 `worlds/` 目录
- 每个世界有独立的文件夹
- 支持多个世界（通过 `--world` 参数）

## 故障排除

### 常见问题

**1. 无法连接到服务器**
- 检查服务器是否正在运行
- 确认端口25565未被其他程序占用
- 检查防火墙设置
- 验证Minecraft客户端版本（需要1.20.1）

**2. 服务器启动失败**
```bash
# 检查Python版本
python --version  # 需要3.8+

# 检查依赖
pip install -r requirements.txt

# 检查端口占用
netstat -an | grep 25565
```

**3. 性能问题**
- 减少最大玩家数
- 降低日志级别到WARNING
- 检查系统资源使用情况

**4. 连接断开**
- 检查网络稳定性
- 查看服务器日志中的错误信息
- 确认客户端版本兼容性

### 调试模式

启用详细日志：
```bash
python start_server.py --log-level DEBUG
```

这将显示：
- 详细的数据包信息
- 玩家连接状态
- 世界生成过程
- 物理引擎状态

## 高级配置

### 修改世界生成
编辑 `server/world/world_generator.py`：
- 修改地形高度
- 添加新的结构
- 改变生物群系

### 添加新方块
在 `server/game/physics.py` 中的 `solid_blocks` 集合添加方块ID。

### 自定义游戏规则
修改 `server/minecraft_server.py` 中的游戏循环逻辑。

## 网络配置

### 局域网游戏
- 使用默认配置即可
- 其他玩家使用你的局域网IP连接

### 公网服务器
1. 配置路由器端口转发（25565端口）
2. 使用公网IP启动：
   ```bash
   python start_server.py --host 0.0.0.0
   ```
3. 告诉朋友你的公网IP地址

### 安全建议
- 不要在生产环境中使用
- 定期备份世界数据
- 监控服务器资源使用
- 限制最大玩家数

## 性能优化

### 服务器性能
- 调整 `max_loaded_chunks` 参数
- 优化区块卸载距离
- 使用SSD存储世界数据

### 网络优化
- 减少不必要的数据包发送
- 优化玩家位置同步频率
- 使用本地网络测试

## 开发和扩展

### 添加新功能
1. 在相应模块中添加代码
2. 更新协议处理
3. 测试功能
4. 更新文档

### 贡献代码
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 支持和帮助

### 获取帮助
- 查看README.md了解项目概述
- 运行测试套件诊断问题
- 检查日志文件获取错误信息

### 报告问题
请提供：
- 操作系统信息
- Python版本
- 错误日志
- 重现步骤

---

**祝你游戏愉快！** 🎮
