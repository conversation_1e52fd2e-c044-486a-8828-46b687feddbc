"""
Connection handler for Minecraft clients
Handles the connection lifecycle and packet routing
"""

import asyncio
import logging
import json
from typing import Dict, Optional

from .packet import PacketBuffer, Packet, read_packet, send_packet
from .protocol import *

logger = logging.getLogger(__name__)

class ConnectionState:
    """Connection states for Minecraft protocol"""
    HANDSHAKING = 0
    STATUS = 1
    LOGIN = 2
    PLAY = 3

class ConnectionHandler:
    """Handles individual client connections"""
    
    def __init__(self, server):
        self.server = server
        
    async def handle_connection(self, reader: asyncio.StreamReader, writer: asyncio.StreamWriter):
        """Handle a client connection through its lifecycle"""
        client_addr = writer.get_extra_info('peername')
        state = ConnectionState.HANDSHAKING
        player = None
        
        logger.info(f"Handling connection from {client_addr}")
        
        try:
            while True:
                # Read packet
                packet_buffer = await read_packet(reader)
                if packet_buffer is None:
                    break
                
                # Handle packet based on current state
                if state == ConnectionState.HANDSHAKING:
                    state = await self._handle_handshaking(packet_buffer, writer)
                elif state == ConnectionState.STATUS:
                    await self._handle_status(packet_buffer, writer)
                elif state == ConnectionState.LOGIN:
                    result = await self._handle_login(packet_buffer, writer)
                    if isinstance(result, tuple):
                        state, player = result
                elif state == ConnectionState.PLAY:
                    if player:
                        await self._handle_play(packet_buffer, writer, player)
                    else:
                        break
                        
        except Exception as e:
            logger.error(f"Error handling connection from {client_addr}: {e}")
        finally:
            if player:
                await self.server.player_manager.remove_player(player)
    
    async def _handle_handshaking(self, packet_buffer: PacketBuffer, writer: asyncio.StreamWriter) -> int:
        """Handle handshaking state packets"""
        packet_id = packet_buffer.read_varint()
        
        if packet_id == 0x00:  # Handshake packet
            protocol_version = packet_buffer.read_varint()
            server_address = packet_buffer.read_string()
            server_port = packet_buffer.read_short()
            next_state = packet_buffer.read_varint()
            
            logger.info(f"Handshake: protocol={protocol_version}, address={server_address}, port={server_port}, next_state={next_state}")
            
            # Return the next state
            return next_state
        
        return ConnectionState.HANDSHAKING
    
    async def _handle_status(self, packet_buffer: PacketBuffer, writer: asyncio.StreamWriter):
        """Handle status state packets (server list ping)"""
        packet_id = packet_buffer.read_varint()
        
        if packet_id == 0x00:  # Status Request
            # Send status response
            status_response = {
                "version": {
                    "name": "1.20.1",
                    "protocol": 763
                },
                "players": {
                    "max": self.server.max_players,
                    "online": len(self.server.player_manager.players),
                    "sample": []
                },
                "description": {
                    "text": self.server.motd
                },
                "favicon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            }
            
            packet = StatusResponsePacket(json.dumps(status_response))
            await send_packet(writer, packet)
            
        elif packet_id == 0x01:  # Ping Request
            payload = packet_buffer.read_long()
            
            # Send pong response
            packet = PongResponsePacket(payload)
            await send_packet(writer, packet)
    
    async def _handle_login(self, packet_buffer: PacketBuffer, writer: asyncio.StreamWriter) -> tuple:
        """Handle login state packets"""
        packet_id = packet_buffer.read_varint()
        
        if packet_id == 0x00:  # Login Start
            username = packet_buffer.read_string()
            
            # For simplicity, we'll skip authentication and directly log in
            logger.info(f"Player {username} attempting to log in")
            
            # Check if server is full
            if len(self.server.player_manager.players) >= self.server.max_players:
                packet = DisconnectPacket("Server is full!")
                await send_packet(writer, packet)
                return ConnectionState.LOGIN, None
            
            # Generate a simple UUID for the player
            import uuid
            player_uuid = str(uuid.uuid4())
            
            # Send login success
            packet = LoginSuccessPacket(player_uuid, username)
            await send_packet(writer, packet)
            
            # Create player object
            from ..player.player import Player
            player = Player(username, player_uuid, writer)
            
            # Add player to manager
            await self.server.player_manager.add_player(player)
            
            # Send join game packet
            await self._send_join_game(writer, player)
            
            return ConnectionState.PLAY, player
        
        return ConnectionState.LOGIN, None
    
    async def _handle_play(self, packet_buffer: PacketBuffer, writer: asyncio.StreamWriter, player):
        """Handle play state packets"""
        packet_id = packet_buffer.read_varint()
        
        # Handle various play packets
        if packet_id == 0x04:  # Chat Message
            message = packet_buffer.read_string()
            await self._handle_chat_message(player, message)
        elif packet_id == 0x13:  # Player Position
            x = packet_buffer.read_double()
            y = packet_buffer.read_double()
            z = packet_buffer.read_double()
            on_ground = packet_buffer.read_bool()
            await self._handle_player_position(player, x, y, z, on_ground)
        elif packet_id == 0x14:  # Player Position and Rotation
            x = packet_buffer.read_double()
            y = packet_buffer.read_double()
            z = packet_buffer.read_double()
            yaw = packet_buffer.read_float()
            pitch = packet_buffer.read_float()
            on_ground = packet_buffer.read_bool()
            await self._handle_player_position_rotation(player, x, y, z, yaw, pitch, on_ground)
        elif packet_id == 0x2C:  # Player Block Placement
            hand = packet_buffer.read_varint()
            location = packet_buffer.read_position()
            face = packet_buffer.read_varint()
            cursor_x = packet_buffer.read_float()
            cursor_y = packet_buffer.read_float()
            cursor_z = packet_buffer.read_float()
            inside_block = packet_buffer.read_bool()
            await self._handle_block_placement(player, hand, location, face, cursor_x, cursor_y, cursor_z, inside_block)
    
    async def _send_join_game(self, writer: asyncio.StreamWriter, player):
        """Send join game packet to player"""
        # This is a simplified join game packet
        packet = JoinGamePacket(
            entity_id=player.entity_id,
            gamemode=1,  # Creative mode
            dimension=0,  # Overworld
            world_name="minecraft:overworld",
            spawn_x=0,
            spawn_y=64,
            spawn_z=0
        )
        await send_packet(writer, packet)
        
        # Send player position
        packet = PlayerPositionAndLookPacket(0, 64, 0, 0, 0)
        await send_packet(writer, packet)
    
    async def _handle_chat_message(self, player, message: str):
        """Handle chat message from player"""
        logger.info(f"<{player.username}> {message}")
        
        # Broadcast message to all players
        chat_packet = ChatMessagePacket(f"<{player.username}> {message}")
        await self.server.player_manager.broadcast_packet(chat_packet)
    
    async def _handle_player_position(self, player, x: float, y: float, z: float, on_ground: bool):
        """Handle player position update"""
        player.x = x
        player.y = y
        player.z = z
        player.on_ground = on_ground
        
        # Broadcast position to other players
        # This would be implemented in a more complete server
    
    async def _handle_player_position_rotation(self, player, x: float, y: float, z: float, yaw: float, pitch: float, on_ground: bool):
        """Handle player position and rotation update"""
        player.x = x
        player.y = y
        player.z = z
        player.yaw = yaw
        player.pitch = pitch
        player.on_ground = on_ground
    
    async def _handle_block_placement(self, player, hand: int, location: tuple, face: int, cursor_x: float, cursor_y: float, cursor_z: float, inside_block: bool):
        """Handle block placement"""
        x, y, z = location
        logger.info(f"Player {player.username} placed block at {x}, {y}, {z}")
        
        # Set block in world
        await self.server.world_manager.set_block(x, y, z, 1)  # Stone block
        
        # Broadcast block change to all players
        block_change_packet = BlockChangePacket(x, y, z, 1)
        await self.server.player_manager.broadcast_packet(block_change_packet)
